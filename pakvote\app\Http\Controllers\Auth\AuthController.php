<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Tenant;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class AuthController extends Controller
{
    /**
     * Show the login form
     */
    public function showLogin()
    {
        return view('auth.login');
    }

    /**
     * Handle login request
     */
    public function login(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'password' => 'required|min:6',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        // Attempt to authenticate user
        $user = User::where('email', $request->email)
                   ->where('is_active', true)
                   ->first();

        if (!$user || !Hash::check($request->password, $user->password)) {
            return back()->withErrors(['email' => 'Invalid credentials.'])->withInput();
        }

        // Get user's tenant
        $tenant = null;
        if ($user->tenant_id) {
            $tenant = Tenant::find($user->tenant_id);

            // Check if tenant is active
            if (!$tenant || !$tenant->is_active) {
                return back()->withErrors(['email' => 'Your organization account is not active.'])->withInput();
            }

            // Check if tenant has active subscription (skip for super admin)
            if (!$user->hasRole('super_admin') && !$tenant->hasActiveSubscription() && !$tenant->isFreePlan()) {
                return back()->withErrors(['email' => 'Organization subscription has expired.'])->withInput();
            }
        }

        try {
            Auth::login($user, $request->filled('remember'));

            // Store tenant in session if exists
            if ($tenant) {
                session(['current_tenant' => $tenant]);
            }

            // Redirect based on user role
            return $this->redirectBasedOnRole($user);

        } catch (\Exception $e) {
            return back()->withErrors(['email' => 'Login failed. Please try again.'])->withInput();
        }
    }

    /**
     * Show the registration form
     */
    public function showRegister()
    {
        return view('auth.register');
    }

    /**
     * Handle registration request
     */
    public function register(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'phone' => 'nullable|string|max:20',
            'cnic' => 'required|string|size:15|unique:users',
            'organization_name' => 'required|string|max:255',
            'user_type' => 'required|in:candidate,agent,volunteer',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            // Create tenant first with auto-generated domain
            $domain = strtolower(str_replace(' ', '-', $request->organization_name)) . '-' . time();

            $tenant = Tenant::create([
                'name' => ['en' => $request->organization_name],
                'domain' => $domain,
                'subscription_plan' => Tenant::PLAN_FREE,
                'subscription_status' => Tenant::STATUS_ACTIVE,
                'contact_email' => $request->email,
                'contact_phone' => $request->phone,
            ]);

            // Create user
            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'phone' => $request->phone,
                'cnic' => $request->cnic,
                'tenant_id' => $tenant->id,
                'user_type' => $request->user_type,
            ]);

            // Assign default role
            $user->assignRole($request->user_type);

            Auth::login($user);
            session(['current_tenant' => $tenant]);

            return redirect()->route('dashboard')->with('success', 'Registration successful! Welcome to PakVote.');

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Registration failed. Please try again.'])->withInput();
        }
    }

    /**
     * Handle logout request
     */
    public function logout(Request $request)
    {
        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->route('login')->with('success', 'You have been logged out successfully.');
    }

    /**
     * Redirect user based on their role
     */
    private function redirectBasedOnRole(User $user)
    {
        switch ($user->user_type) {
            case User::TYPE_ADMIN:
                return redirect()->route('admin.dashboard');
            case User::TYPE_CANDIDATE:
                return redirect()->route('candidate.dashboard');
            case User::TYPE_AGENT:
                return redirect()->route('agent.dashboard');
            case User::TYPE_VOLUNTEER:
                return redirect()->route('volunteer.dashboard');
            default:
                return redirect()->route('dashboard');
        }
    }
}
