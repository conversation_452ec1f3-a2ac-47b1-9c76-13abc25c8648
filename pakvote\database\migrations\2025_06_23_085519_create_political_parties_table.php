<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('political_parties', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->json('name'); // Translatable field
            $table->string('short_name');
            $table->string('symbol')->unique();
            $table->string('symbol_image')->nullable();
            $table->json('description')->nullable(); // Translatable field
            $table->date('founded_date')->nullable();
            $table->string('headquarters')->nullable();
            $table->string('website')->nullable();
            $table->string('contact_email')->nullable();
            $table->string('contact_phone')->nullable();
            $table->boolean('is_active')->default(true);
            $table->string('ecp_registration_number')->nullable()->unique();
            $table->string('party_head')->nullable();
            $table->timestamps();

            $table->index('is_active');
            $table->index('symbol');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('political_parties');
    }
};
