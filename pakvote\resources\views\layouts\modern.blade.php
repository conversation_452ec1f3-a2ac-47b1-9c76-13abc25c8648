<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@yield('title', 'PakVote') - Election Management System</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Nastaliq+Urdu:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Modern Components CSS -->
    <link href="{{ asset('css/modern-components.css') }}" rel="stylesheet">

    @stack('styles')
</head>
<body>
    <!-- Header Component -->
    @include('components.header')

    <div class="app-container">
        @hasSection('sidebar')
            <!-- Modern Sidebar -->
            @include('components.sidebar')
            
            <!-- Main Content with Sidebar -->
            <div class="main-content with-sidebar">
                <div class="content-wrapper">
                    <!-- Flash Messages -->
                    @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show modern-alert" role="alert">
                            <div class="alert-content">
                                <i class="bi bi-check-circle alert-icon"></i>
                                <div class="alert-text">
                                    <strong>Success!</strong>
                                    <p>{{ session('success') }}</p>
                                </div>
                            </div>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger alert-dismissible fade show modern-alert" role="alert">
                            <div class="alert-content">
                                <i class="bi bi-exclamation-triangle alert-icon"></i>
                                <div class="alert-text">
                                    <strong>Error!</strong>
                                    <p>{{ session('error') }}</p>
                                </div>
                            </div>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    @if($errors->any())
                        <div class="alert alert-danger alert-dismissible fade show modern-alert" role="alert">
                            <div class="alert-content">
                                <i class="bi bi-exclamation-triangle alert-icon"></i>
                                <div class="alert-text">
                                    <strong>Please fix the following errors:</strong>
                                    <ul class="mb-0 mt-2">
                                        @foreach($errors->all() as $error)
                                            <li>{{ $error }}</li>
                                        @endforeach
                                    </ul>
                                </div>
                            </div>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    <!-- Page Content -->
                    <main class="page-content">
                        @yield('content')
                    </main>
                </div>
            </div>
        @else
            <!-- Main Content without Sidebar -->
            <div class="main-content">
                <div class="container-fluid">
                    <!-- Flash Messages -->
                    @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show modern-alert" role="alert">
                            <div class="alert-content">
                                <i class="bi bi-check-circle alert-icon"></i>
                                <div class="alert-text">
                                    <strong>Success!</strong>
                                    <p>{{ session('success') }}</p>
                                </div>
                            </div>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger alert-dismissible fade show modern-alert" role="alert">
                            <div class="alert-content">
                                <i class="bi bi-exclamation-triangle alert-icon"></i>
                                <div class="alert-text">
                                    <strong>Error!</strong>
                                    <p>{{ session('error') }}</p>
                                </div>
                            </div>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    @if($errors->any())
                        <div class="alert alert-danger alert-dismissible fade show modern-alert" role="alert">
                            <div class="alert-content">
                                <i class="bi bi-exclamation-triangle alert-icon"></i>
                                <div class="alert-text">
                                    <strong>Please fix the following errors:</strong>
                                    <ul class="mb-0 mt-2">
                                        @foreach($errors->all() as $error)
                                            <li>{{ $error }}</li>
                                        @endforeach
                                    </ul>
                                </div>
                            </div>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    <!-- Page Content -->
                    <main class="page-content">
                        @yield('content')
                    </main>
                </div>
            </div>
        @endif
    </div>

    <!-- Footer Component -->
    @include('components.footer')

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Modern Components JS -->
    <script>
        // Sidebar Toggle Functionality
        document.addEventListener('DOMContentLoaded', function() {
            const sidebar = document.getElementById('sidebar');
            const sidebarToggle = document.getElementById('sidebarToggle');
            const sidebarOverlay = document.getElementById('sidebarOverlay');
            
            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('collapsed');
                });
            }
            
            // Mobile sidebar toggle
            if (window.innerWidth <= 768) {
                if (sidebarOverlay) {
                    sidebarOverlay.addEventListener('click', function() {
                        sidebar.classList.remove('show');
                        sidebarOverlay.classList.remove('show');
                    });
                }
            }
            
            // Auto-hide alerts after 5 seconds
            const alerts = document.querySelectorAll('.modern-alert');
            alerts.forEach(alert => {
                setTimeout(() => {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }, 5000);
            });
        });
    </script>
    
    @stack('scripts')
</body>
</html>
