<?php $__env->startSection('title', 'Manage Political Parties'); ?>

<?php $__env->startSection('sidebar'); ?>
<div class="list-group list-group-flush">
    <a href="<?php echo e(route('admin.dashboard')); ?>" class="list-group-item list-group-item-action">
        <i class="bi bi-speedometer2"></i> Dashboard
    </a>
    <a href="<?php echo e(route('admin.users')); ?>" class="list-group-item list-group-item-action">
        <i class="bi bi-people"></i> Users
    </a>
    <a href="<?php echo e(route('admin.tenants')); ?>" class="list-group-item list-group-item-action">
        <i class="bi bi-building"></i> Organizations
    </a>
    <a href="<?php echo e(route('admin.parties')); ?>" class="list-group-item list-group-item-action active">
        <i class="bi bi-flag"></i> Political Parties
    </a>
    <a href="<?php echo e(route('admin.constituencies')); ?>" class="list-group-item list-group-item-action">
        <i class="bi bi-geo-alt"></i> Constituencies
    </a>
    <a href="<?php echo e(route('admin.settings')); ?>" class="list-group-item list-group-item-action">
        <i class="bi bi-gear"></i> Settings
    </a>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="bi bi-flag text-success"></i> Manage Political Parties
    </h1>
    <div>
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addPartyModal">
            <i class="bi bi-plus"></i> Add Party
        </button>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card border-success">
            <div class="card-body text-center">
                <i class="bi bi-flag display-6 text-success"></i>
                <h4 class="mt-2"><?php echo e($parties->total()); ?></h4>
                <p class="text-muted mb-0">Total Parties</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card border-primary">
            <div class="card-body text-center">
                <i class="bi bi-check-circle display-6 text-primary"></i>
                <h4 class="mt-2"><?php echo e($parties->where('is_active', true)->count()); ?></h4>
                <p class="text-muted mb-0">Active Parties</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card border-info">
            <div class="card-body text-center">
                <i class="bi bi-people display-6 text-info"></i>
                <h4 class="mt-2"><?php echo e($parties->sum('candidates_count')); ?></h4>
                <p class="text-muted mb-0">Total Candidates</p>
            </div>
        </div>
    </div>
</div>

<!-- Parties Table -->
<div class="card">
    <div class="card-header bg-light">
        <h5 class="mb-0">
            <i class="bi bi-list"></i> Political Parties List
        </h5>
    </div>
    <div class="card-body">
        <?php if($parties->count() > 0): ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>Party</th>
                            <th>Symbol</th>
                            <th>Contact</th>
                            <th>ECP Registration</th>
                            <th>Candidates</th>
                            <th>Status</th>
                            <th>Founded</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $parties; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $party): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <?php if($party->symbol_image): ?>
                                            <img src="<?php echo e($party->symbol_image); ?>" alt="Symbol" class="me-2" width="32" height="32">
                                        <?php else: ?>
                                            <div class="bg-success text-white rounded d-flex align-items-center justify-content-center me-2" style="width: 32px; height: 32px;">
                                                <?php echo e(strtoupper(substr($party->short_name, 0, 1))); ?>

                                            </div>
                                        <?php endif; ?>
                                        <div>
                                            <h6 class="mb-0"><?php echo e($party->name['en'] ?? $party->name); ?></h6>
                                            <small class="text-muted"><?php echo e($party->short_name); ?></small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-light text-dark border"><?php echo e($party->symbol); ?></span>
                                </td>
                                <td>
                                    <div>
                                        <?php if($party->contact_email): ?>
                                            <small class="d-block"><?php echo e($party->contact_email); ?></small>
                                        <?php endif; ?>
                                        <?php if($party->contact_phone): ?>
                                            <small class="text-muted"><?php echo e($party->contact_phone); ?></small>
                                        <?php endif; ?>
                                        <?php if($party->website): ?>
                                            <small class="text-primary d-block">
                                                <a href="<?php echo e($party->website); ?>" target="_blank"><?php echo e($party->website); ?></a>
                                            </small>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <?php if($party->ecp_registration_number): ?>
                                        <code><?php echo e($party->ecp_registration_number); ?></code>
                                    <?php else: ?>
                                        <span class="text-muted">Not registered</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge bg-info"><?php echo e($party->candidates_count ?? 0); ?></span>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo e($party->is_active ? 'success' : 'secondary'); ?>">
                                        <?php echo e($party->is_active ? 'Active' : 'Inactive'); ?>

                                    </span>
                                </td>
                                <td>
                                    <?php if($party->founded_date): ?>
                                        <small><?php echo e($party->founded_date->format('Y')); ?></small>
                                    <?php else: ?>
                                        <span class="text-muted">Unknown</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button type="button" class="btn btn-outline-primary" title="Edit">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-info" title="View Candidates">
                                            <i class="bi bi-people"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-<?php echo e($party->is_active ? 'warning' : 'success'); ?>" title="<?php echo e($party->is_active ? 'Deactivate' : 'Activate'); ?>">
                                            <i class="bi bi-<?php echo e($party->is_active ? 'pause' : 'play'); ?>"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-4">
                <?php echo e($parties->links()); ?>

            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <i class="bi bi-flag display-1 text-muted"></i>
                <h4 class="text-muted mt-3">No Political Parties Found</h4>
                <p class="text-muted">Start by adding your first political party.</p>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addPartyModal">
                    <i class="bi bi-plus"></i> Add Party
                </button>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Add Party Modal -->
<div class="modal fade" id="addPartyModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Political Party</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="party_name" class="form-label">Party Name (English)</label>
                                <input type="text" class="form-control" id="party_name" required>
                            </div>
                            <div class="mb-3">
                                <label for="party_name_urdu" class="form-label">Party Name (Urdu)</label>
                                <input type="text" class="form-control urdu-text" id="party_name_urdu" dir="rtl">
                            </div>
                            <div class="mb-3">
                                <label for="short_name" class="form-label">Short Name</label>
                                <input type="text" class="form-control" id="short_name" required>
                            </div>
                            <div class="mb-3">
                                <label for="symbol" class="form-label">Election Symbol</label>
                                <input type="text" class="form-control" id="symbol" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="contact_email" class="form-label">Contact Email</label>
                                <input type="email" class="form-control" id="contact_email">
                            </div>
                            <div class="mb-3">
                                <label for="contact_phone" class="form-label">Contact Phone</label>
                                <input type="tel" class="form-control" id="contact_phone">
                            </div>
                            <div class="mb-3">
                                <label for="website" class="form-label">Website</label>
                                <input type="url" class="form-control" id="website">
                            </div>
                            <div class="mb-3">
                                <label for="ecp_registration" class="form-label">ECP Registration Number</label>
                                <input type="text" class="form-control" id="ecp_registration">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="party_head" class="form-label">Party Head</label>
                        <input type="text" class="form-control" id="party_head">
                    </div>
                    <div class="mb-3">
                        <label for="headquarters" class="form-label">Headquarters</label>
                        <input type="text" class="form-control" id="headquarters">
                    </div>
                    <div class="mb-3">
                        <label for="founded_date" class="form-label">Founded Date</label>
                        <input type="date" class="form-control" id="founded_date">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Party</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .table th {
        border-top: none;
        font-weight: 600;
    }
    .card {
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .urdu-text {
        font-family: 'Noto Nastaliq Urdu', serif;
    }
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\ECP\pakvote\resources\views/admin/parties.blade.php ENDPATH**/ ?>