@props([
    'title' => null,
    'subtitle' => null,
    'icon' => null,
    'headerClass' => 'bg-primary text-white',
    'bodyClass' => '',
    'footerClass' => 'bg-light',
    'footer' => null,
    'actions' => null,
    'stats' => null,
    'gradient' => false,
    'hover' => false,
    'shadow' => 'medium'
])

<!-- Modern Card Component -->
<div class="modern-card {{ $gradient ? 'gradient-card' : '' }} {{ $hover ? 'hover-card' : '' }} shadow-{{ $shadow }}" {{ $attributes }}>
    @if($title || $icon || $actions)
        <div class="card-header {{ $headerClass }}">
            <div class="d-flex align-items-center justify-content-between">
                <div class="header-content">
                    @if($icon)
                        <div class="header-icon">
                            <i class="{{ $icon }}"></i>
                        </div>
                    @endif
                    <div class="header-text">
                        @if($title)
                            <h5 class="card-title mb-0">{{ $title }}</h5>
                        @endif
                        @if($subtitle)
                            <small class="card-subtitle">{{ $subtitle }}</small>
                        @endif
                    </div>
                </div>
                @if($actions)
                    <div class="header-actions">
                        {{ $actions }}
                    </div>
                @endif
            </div>
        </div>
    @endif

    @if($stats)
        <div class="card-stats">
            <div class="row g-0">
                @foreach($stats as $stat)
                    <div class="col">
                        <div class="stat-item">
                            @if(isset($stat['icon']))
                                <div class="stat-icon">
                                    <i class="{{ $stat['icon'] }}"></i>
                                </div>
                            @endif
                            <div class="stat-content">
                                <div class="stat-value">{{ $stat['value'] }}</div>
                                <div class="stat-label">{{ $stat['label'] }}</div>
                                @if(isset($stat['change']))
                                    <div class="stat-change {{ $stat['change'] >= 0 ? 'positive' : 'negative' }}">
                                        <i class="bi bi-{{ $stat['change'] >= 0 ? 'arrow-up' : 'arrow-down' }}"></i>
                                        {{ abs($stat['change']) }}%
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    @endif

    <div class="card-body {{ $bodyClass }}">
        {{ $slot }}
    </div>

    @if($footer)
        <div class="card-footer {{ $footerClass }}">
            {{ $footer }}
        </div>
    @endif
</div>

<style>
/* Modern Card Styles */
.modern-card {
    border: none;
    border-radius: 16px;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: white;
}

.modern-card.shadow-light {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.modern-card.shadow-medium {
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.modern-card.shadow-heavy {
    box-shadow: 0 8px 16px rgba(0,0,0,0.2);
}

.modern-card.hover-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 24px rgba(0,0,0,0.2);
}

.modern-card.gradient-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.modern-card .card-header {
    border: none;
    padding: 20px;
    border-radius: 0;
}

.modern-card .card-body {
    padding: 20px;
}

.modern-card .card-footer {
    border: none;
    padding: 15px 20px;
}

/* Header Styles */
.header-content {
    display: flex;
    align-items: center;
    gap: 12px;
}

.header-icon {
    width: 40px;
    height: 40px;
    background: rgba(255,255,255,0.2);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.header-text .card-title {
    font-weight: 600;
    font-size: 1.1rem;
}

.header-text .card-subtitle {
    opacity: 0.8;
    font-size: 0.85rem;
}

.header-actions {
    display: flex;
    gap: 8px;
}

/* Stats Section */
.card-stats {
    background: rgba(0,0,0,0.05);
    border-bottom: 1px solid rgba(0,0,0,0.1);
}

.stat-item {
    padding: 20px;
    text-align: center;
    border-right: 1px solid rgba(0,0,0,0.1);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.stat-item:last-child {
    border-right: none;
}

.stat-icon {
    width: 32px;
    height: 32px;
    background: var(--primary-color);
    color: white;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.stat-label {
    font-size: 0.8rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.stat-change {
    font-size: 0.75rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 4px;
}

.stat-change.positive {
    color: #28a745;
}

.stat-change.negative {
    color: #dc3545;
}

/* Responsive */
@media (max-width: 768px) {
    .modern-card .card-header,
    .modern-card .card-body,
    .modern-card .card-footer {
        padding: 15px;
    }
    
    .header-content {
        flex-direction: column;
        text-align: center;
        gap: 8px;
    }
    
    .stat-item {
        padding: 15px 10px;
    }
    
    .stat-value {
        font-size: 1.2rem;
    }
}
</style>
