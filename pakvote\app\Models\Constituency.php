<?php

namespace App\Models;

use App\Traits\HasUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\Translatable\HasTranslations;

class Constituency extends Model
{
    use HasFactory, HasUuid, HasTranslations;

    protected $fillable = [
        'name',
        'code',
        'type',
        'province',
        'district',
        'tehsil',
        'total_voters',
        'total_polling_stations',
        'area_description',
        'boundaries',
        'is_active',
        'ecp_code',
    ];

    protected $casts = [
        'boundaries' => 'array',
        'is_active' => 'boolean',
        'total_voters' => 'integer',
        'total_polling_stations' => 'integer',
    ];

    public $translatable = ['name', 'area_description'];

    /**
     * Constituency types
     */
    const TYPE_NATIONAL = 'national';
    const TYPE_PROVINCIAL = 'provincial';
    const TYPE_LOCAL = 'local';

    /**
     * Pakistani provinces
     */
    const PROVINCE_PUNJAB = 'punjab';
    const PROVINCE_SINDH = 'sindh';
    const PROVINCE_KPK = 'kpk';
    const PROVINCE_BALOCHISTAN = 'balochistan';
    const PROVINCE_ISLAMABAD = 'islamabad';
    const PROVINCE_GILGIT_BALTISTAN = 'gilgit_baltistan';
    const PROVINCE_AJK = 'ajk';

    /**
     * Get the candidates for the constituency.
     */
    public function candidates(): HasMany
    {
        return $this->hasMany(Candidate::class);
    }

    /**
     * Get the voters for the constituency.
     */
    public function voters(): HasMany
    {
        return $this->hasMany(Voter::class);
    }

    /**
     * Scope for active constituencies
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope by type
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope by province
     */
    public function scopeByProvince($query, $province)
    {
        return $query->where('province', $province);
    }

    /**
     * Get full constituency name with code
     */
    public function getFullNameAttribute(): string
    {
        return $this->code . ' - ' . $this->name;
    }
}
