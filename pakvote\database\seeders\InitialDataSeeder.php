<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Tenant;
use App\Models\PoliticalParty;
use App\Models\Constituency;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class InitialDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create super admin tenant
        $superAdminTenant = Tenant::create([
            'name' => ['en' => 'PakVote System Administration'],
            'domain' => 'admin.pakvote.com',
            'subscription_plan' => Tenant::PLAN_ENTERPRISE,
            'subscription_status' => Tenant::STATUS_ACTIVE,
            'contact_email' => '<EMAIL>',
            'is_active' => true,
        ]);

        // Create super admin user
        $superAdmin = User::create([
            'name' => 'Super Administrator',
            'email' => '<EMAIL>',
            'password' => Hash::make('admin123'),
            'phone' => '+92-300-1234567',
            'cnic' => '12345-6789012-3',
            'tenant_id' => $superAdminTenant->id,
            'user_type' => User::TYPE_ADMIN,
            'is_active' => true,
        ]);

        $superAdmin->assignRole('super_admin');

        // Create sample political parties
        $parties = [
            [
                'name' => ['en' => 'Pakistan Tehreek-e-Insaf', 'ur' => 'پاکستان تحریک انصاف'],
                'short_name' => 'PTI',
                'symbol' => 'Cricket Bat',
                'description' => ['en' => 'Pakistan Tehreek-e-Insaf political party'],
                'ecp_registration_number' => 'PTI-001',
            ],
            [
                'name' => ['en' => 'Pakistan Muslim League (Nawaz)', 'ur' => 'پاکستان مسلم لیگ (ن)'],
                'short_name' => 'PML-N',
                'symbol' => 'Tiger',
                'description' => ['en' => 'Pakistan Muslim League (Nawaz) political party'],
                'ecp_registration_number' => 'PMLN-001',
            ],
            [
                'name' => ['en' => 'Pakistan Peoples Party', 'ur' => 'پاکستان پیپلز پارٹی'],
                'short_name' => 'PPP',
                'symbol' => 'Arrow',
                'description' => ['en' => 'Pakistan Peoples Party political party'],
                'ecp_registration_number' => 'PPP-001',
            ],
        ];

        foreach ($parties as $party) {
            PoliticalParty::create($party);
        }

        // Create sample constituencies
        $constituencies = [
            [
                'name' => ['en' => 'NA-1 Chitral-cum-Upper Dir-cum-Lower Dir'],
                'code' => 'NA-1',
                'type' => Constituency::TYPE_NATIONAL,
                'province' => Constituency::PROVINCE_KPK,
                'district' => 'Chitral',
                'total_voters' => 450000,
                'total_polling_stations' => 350,
                'ecp_code' => 'NA-001-KPK',
            ],
            [
                'name' => ['en' => 'PP-1 Attock-I'],
                'code' => 'PP-1',
                'type' => Constituency::TYPE_PROVINCIAL,
                'province' => Constituency::PROVINCE_PUNJAB,
                'district' => 'Attock',
                'total_voters' => 280000,
                'total_polling_stations' => 220,
                'ecp_code' => 'PP-001-PUN',
            ],
            [
                'name' => ['en' => 'PS-1 Karachi East-I'],
                'code' => 'PS-1',
                'type' => Constituency::TYPE_PROVINCIAL,
                'province' => Constituency::PROVINCE_SINDH,
                'district' => 'Karachi East',
                'total_voters' => 320000,
                'total_polling_stations' => 280,
                'ecp_code' => 'PS-001-SIN',
            ],
        ];

        foreach ($constituencies as $constituency) {
            Constituency::create($constituency);
        }

        $this->command->info('Initial data seeded successfully!');
        $this->command->info('Super Admin Login:');
        $this->command->info('Email: <EMAIL>');
        $this->command->info('Password: admin123');
        $this->command->info('Domain: admin.pakvote.com');
    }
}
