<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('constituencies', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->json('name'); // Translatable field
            $table->string('code')->unique();
            $table->enum('type', ['national', 'provincial', 'local']);
            $table->enum('province', ['punjab', 'sindh', 'kpk', 'balochistan', 'islamabad', 'gilgit_baltistan', 'ajk']);
            $table->string('district');
            $table->string('tehsil')->nullable();
            $table->integer('total_voters')->default(0);
            $table->integer('total_polling_stations')->default(0);
            $table->json('area_description')->nullable(); // Translatable field
            $table->json('boundaries')->nullable();
            $table->boolean('is_active')->default(true);
            $table->string('ecp_code')->nullable()->unique();
            $table->timestamps();

            $table->index(['type', 'province']);
            $table->index('is_active');
            $table->index('code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('constituencies');
    }
};
