<?php

namespace App\Http\Middleware;

use App\Models\Tenant;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class TenantMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Skip tenant check for login/register routes
        if ($request->routeIs(['login', 'register', 'logout'])) {
            return $next($request);
        }

        // Check if user is authenticated
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $user = Auth::user();

        // Skip tenant checks for super admin
        if ($user->hasRole('super_admin')) {
            return $next($request);
        }

        // Get tenant from session or user
        $tenant = session('current_tenant');

        if (!$tenant && $user->tenant_id) {
            $tenant = Tenant::find($user->tenant_id);
            if ($tenant) {
                session(['current_tenant' => $tenant]);
            }
        }

        // For non-super admin users, tenant is required
        if (!$tenant) {
            Auth::logout();
            return redirect()->route('login')->withErrors(['error' => 'No organization associated with your account.']);
        }

        // Verify tenant is active
        if (!$tenant->is_active) {
            Auth::logout();
            return redirect()->route('login')->withErrors(['error' => 'Organization is not active.']);
        }

        // Check subscription status for non-free plans
        if (!$tenant->hasActiveSubscription() && !$tenant->isFreePlan()) {
            return redirect()->route('subscription.expired');
        }

        // Verify user belongs to this tenant
        if ($user->tenant_id !== $tenant->id) {
            Auth::logout();
            return redirect()->route('login')->withErrors(['error' => 'Access denied.']);
        }

        // Set tenant context for the request
        $request->attributes->set('tenant', $tenant);

        return $next($request);
    }
}
