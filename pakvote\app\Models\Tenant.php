<?php

namespace App\Models;

use App\Traits\HasUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\Translatable\HasTranslations;

class Tenant extends Model
{
    use HasFactory, HasUuid, HasTranslations;

    protected $fillable = [
        'name',
        'domain',
        'database',
        'subscription_plan',
        'subscription_status',
        'subscription_ends_at',
        'settings',
        'is_active',
        'contact_email',
        'contact_phone',
        'address',
        'logo',
    ];

    protected $casts = [
        'settings' => 'array',
        'is_active' => 'boolean',
        'subscription_ends_at' => 'datetime',
    ];

    public $translatable = ['name'];

    /**
     * Subscription plans
     */
    const PLAN_FREE = 'free';
    const PLAN_PRO = 'pro';
    const PLAN_ENTERPRISE = 'enterprise';

    /**
     * Subscription statuses
     */
    const STATUS_ACTIVE = 'active';
    const STATUS_INACTIVE = 'inactive';
    const STATUS_SUSPENDED = 'suspended';
    const STATUS_CANCELLED = 'cancelled';

    /**
     * Get the users for the tenant.
     */
    public function users(): HasMany
    {
        return $this->hasMany(User::class);
    }

    /**
     * Get the candidates for the tenant.
     */
    public function candidates(): HasMany
    {
        return $this->hasMany(Candidate::class);
    }

    /**
     * Get the campaigns for the tenant.
     */
    public function campaigns(): HasMany
    {
        return $this->hasMany(Campaign::class);
    }

    /**
     * Check if tenant has active subscription
     */
    public function hasActiveSubscription(): bool
    {
        return $this->subscription_status === self::STATUS_ACTIVE
            && $this->subscription_ends_at > now();
    }

    /**
     * Check if tenant is on free plan
     */
    public function isFreePlan(): bool
    {
        return $this->subscription_plan === self::PLAN_FREE;
    }

    /**
     * Check if tenant is on pro plan
     */
    public function isProPlan(): bool
    {
        return $this->subscription_plan === self::PLAN_PRO;
    }

    /**
     * Check if tenant is on enterprise plan
     */
    public function isEnterprisePlan(): bool
    {
        return $this->subscription_plan === self::PLAN_ENTERPRISE;
    }
}
