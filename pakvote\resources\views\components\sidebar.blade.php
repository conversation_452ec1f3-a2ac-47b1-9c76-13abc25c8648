<!-- Modern Sidebar Component -->
<div class="modern-sidebar" id="sidebar">
    <!-- Sidebar Header -->
    <div class="sidebar-header">
        <div class="sidebar-brand">
            <div class="brand-icon">
                <i class="bi bi-check-circle-fill"></i>
            </div>
            <div class="brand-text">
                <span class="brand-name">PakVote</span>
                <span class="brand-subtitle">{{ ucfirst(auth()->user()->user_type ?? 'User') }} Panel</span>
            </div>
        </div>
        <button class="sidebar-toggle" id="sidebarToggle">
            <i class="bi bi-chevron-left"></i>
        </button>
    </div>

    <!-- User Profile Section -->
    <div class="sidebar-profile">
        <div class="profile-avatar">
            <img src="https://ui-avatars.com/api/?name={{ urlencode(auth()->user()->name ?? 'User') }}&background=2d5a27&color=fff" 
                 alt="Profile" class="avatar-img">
            <div class="status-indicator online"></div>
        </div>
        <div class="profile-info">
            <h6 class="profile-name">{{ auth()->user()->name ?? 'User Name' }}</h6>
            <p class="profile-role">{{ ucfirst(auth()->user()->user_type ?? 'user') }}</p>
            @if(session('current_tenant'))
                <small class="profile-org">{{ session('current_tenant')->name['en'] ?? session('current_tenant')->name }}</small>
            @endif
        </div>
    </div>

    <!-- Navigation Menu -->
    <nav class="sidebar-nav">
        @if(auth()->check() && auth()->user()->isAdmin())
            <!-- Admin Navigation -->
            <div class="nav-section">
                <h6 class="nav-section-title">Administration</h6>
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="{{ route('admin.dashboard') }}" class="nav-link {{ request()->routeIs('admin.dashboard') ? 'active' : '' }}">
                            <div class="nav-icon">
                                <i class="bi bi-speedometer2"></i>
                            </div>
                            <span class="nav-text">Dashboard</span>
                            <div class="nav-indicator"></div>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="{{ route('admin.users') }}" class="nav-link {{ request()->routeIs('admin.users*') ? 'active' : '' }}">
                            <div class="nav-icon">
                                <i class="bi bi-people"></i>
                            </div>
                            <span class="nav-text">Users</span>
                            <span class="nav-badge">{{ \App\Models\User::count() ?? 0 }}</span>
                            <div class="nav-indicator"></div>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="{{ route('admin.tenants') }}" class="nav-link {{ request()->routeIs('admin.tenants*') ? 'active' : '' }}">
                            <div class="nav-icon">
                                <i class="bi bi-building"></i>
                            </div>
                            <span class="nav-text">Organizations</span>
                            <span class="nav-badge">{{ \App\Models\Tenant::count() ?? 0 }}</span>
                            <div class="nav-indicator"></div>
                        </a>
                    </li>
                </ul>
            </div>

            <div class="nav-section">
                <h6 class="nav-section-title">Election Management</h6>
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="{{ route('admin.parties') }}" class="nav-link {{ request()->routeIs('admin.parties*') ? 'active' : '' }}">
                            <div class="nav-icon">
                                <i class="bi bi-flag"></i>
                            </div>
                            <span class="nav-text">Political Parties</span>
                            <span class="nav-badge">{{ \App\Models\PoliticalParty::count() ?? 0 }}</span>
                            <div class="nav-indicator"></div>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="{{ route('admin.constituencies') }}" class="nav-link {{ request()->routeIs('admin.constituencies*') ? 'active' : '' }}">
                            <div class="nav-icon">
                                <i class="bi bi-geo-alt"></i>
                            </div>
                            <span class="nav-text">Constituencies</span>
                            <span class="nav-badge">{{ \App\Models\Constituency::count() ?? 0 }}</span>
                            <div class="nav-indicator"></div>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <div class="nav-icon">
                                <i class="bi bi-person-badge"></i>
                            </div>
                            <span class="nav-text">Candidates</span>
                            <span class="nav-badge">{{ \App\Models\Candidate::count() ?? 0 }}</span>
                            <div class="nav-indicator"></div>
                        </a>
                    </li>
                </ul>
            </div>

            <div class="nav-section">
                <h6 class="nav-section-title">System</h6>
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="{{ route('admin.settings') }}" class="nav-link {{ request()->routeIs('admin.settings*') ? 'active' : '' }}">
                            <div class="nav-icon">
                                <i class="bi bi-gear"></i>
                            </div>
                            <span class="nav-text">Settings</span>
                            <div class="nav-indicator"></div>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <div class="nav-icon">
                                <i class="bi bi-graph-up"></i>
                            </div>
                            <span class="nav-text">Analytics</span>
                            <div class="nav-indicator"></div>
                        </a>
                    </li>
                </ul>
            </div>

        @elseif(auth()->check() && auth()->user()->isCandidate())
            <!-- Candidate Navigation -->
            <div class="nav-section">
                <h6 class="nav-section-title">Campaign Management</h6>
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="{{ route('candidate.dashboard') }}" class="nav-link {{ request()->routeIs('candidate.dashboard') ? 'active' : '' }}">
                            <div class="nav-icon">
                                <i class="bi bi-speedometer2"></i>
                            </div>
                            <span class="nav-text">Dashboard</span>
                            <div class="nav-indicator"></div>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="{{ route('candidate.profile') }}" class="nav-link {{ request()->routeIs('candidate.profile*') ? 'active' : '' }}">
                            <div class="nav-icon">
                                <i class="bi bi-person"></i>
                            </div>
                            <span class="nav-text">My Profile</span>
                            <div class="nav-indicator"></div>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="{{ route('candidate.campaign') }}" class="nav-link {{ request()->routeIs('candidate.campaign*') ? 'active' : '' }}">
                            <div class="nav-icon">
                                <i class="bi bi-megaphone"></i>
                            </div>
                            <span class="nav-text">Campaign</span>
                            <div class="nav-indicator"></div>
                        </a>
                    </li>
                </ul>
            </div>

            <div class="nav-section">
                <h6 class="nav-section-title">Voter Outreach</h6>
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="{{ route('candidate.voters') }}" class="nav-link {{ request()->routeIs('candidate.voters*') ? 'active' : '' }}">
                            <div class="nav-icon">
                                <i class="bi bi-people"></i>
                            </div>
                            <span class="nav-text">Voters</span>
                            <span class="nav-badge">0</span>
                            <div class="nav-indicator"></div>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="{{ route('candidate.volunteers') }}" class="nav-link {{ request()->routeIs('candidate.volunteers*') ? 'active' : '' }}">
                            <div class="nav-icon">
                                <i class="bi bi-person-hearts"></i>
                            </div>
                            <span class="nav-text">Volunteers</span>
                            <span class="nav-badge">0</span>
                            <div class="nav-indicator"></div>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <div class="nav-icon">
                                <i class="bi bi-chat-dots"></i>
                            </div>
                            <span class="nav-text">SMS Campaign</span>
                            <div class="nav-indicator"></div>
                        </a>
                    </li>
                </ul>
            </div>

            <div class="nav-section">
                <h6 class="nav-section-title">Reports</h6>
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="{{ route('candidate.reports') }}" class="nav-link {{ request()->routeIs('candidate.reports*') ? 'active' : '' }}">
                            <div class="nav-icon">
                                <i class="bi bi-graph-up"></i>
                            </div>
                            <span class="nav-text">Analytics</span>
                            <div class="nav-indicator"></div>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <div class="nav-icon">
                                <i class="bi bi-file-earmark-text"></i>
                            </div>
                            <span class="nav-text">Reports</span>
                            <div class="nav-indicator"></div>
                        </a>
                    </li>
                </ul>
            </div>
        @endif
    </nav>

    <!-- Sidebar Footer -->
    <div class="sidebar-footer">
        <div class="footer-stats">
            <div class="stat-item">
                <i class="bi bi-clock"></i>
                <span>{{ now()->format('H:i') }}</span>
            </div>
            <div class="stat-item">
                <i class="bi bi-calendar"></i>
                <span>{{ now()->format('M j') }}</span>
            </div>
        </div>
        <div class="footer-actions">
            <button class="btn btn-sm btn-outline-light" title="Help">
                <i class="bi bi-question-circle"></i>
            </button>
            <button class="btn btn-sm btn-outline-light" title="Settings">
                <i class="bi bi-gear"></i>
            </button>
        </div>
    </div>
</div>

<!-- Sidebar Overlay for Mobile -->
<div class="sidebar-overlay" id="sidebarOverlay"></div>
