@extends('layouts.app')

@section('title', 'Manage Political Parties')

@section('sidebar')
<div class="list-group list-group-flush">
    <a href="{{ route('admin.dashboard') }}" class="list-group-item list-group-item-action">
        <i class="bi bi-speedometer2"></i> Dashboard
    </a>
    <a href="{{ route('admin.users') }}" class="list-group-item list-group-item-action">
        <i class="bi bi-people"></i> Users
    </a>
    <a href="{{ route('admin.tenants') }}" class="list-group-item list-group-item-action">
        <i class="bi bi-building"></i> Organizations
    </a>
    <a href="{{ route('admin.parties') }}" class="list-group-item list-group-item-action active">
        <i class="bi bi-flag"></i> Political Parties
    </a>
    <a href="{{ route('admin.constituencies') }}" class="list-group-item list-group-item-action">
        <i class="bi bi-geo-alt"></i> Constituencies
    </a>
    <a href="{{ route('admin.settings') }}" class="list-group-item list-group-item-action">
        <i class="bi bi-gear"></i> Settings
    </a>
</div>
@endsection

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="bi bi-flag text-success"></i> Manage Political Parties
    </h1>
    <div>
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addPartyModal">
            <i class="bi bi-plus"></i> Add Party
        </button>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card border-success">
            <div class="card-body text-center">
                <i class="bi bi-flag display-6 text-success"></i>
                <h4 class="mt-2">{{ $parties->total() }}</h4>
                <p class="text-muted mb-0">Total Parties</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card border-primary">
            <div class="card-body text-center">
                <i class="bi bi-check-circle display-6 text-primary"></i>
                <h4 class="mt-2">{{ $parties->where('is_active', true)->count() }}</h4>
                <p class="text-muted mb-0">Active Parties</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card border-info">
            <div class="card-body text-center">
                <i class="bi bi-people display-6 text-info"></i>
                <h4 class="mt-2">{{ $parties->sum('candidates_count') }}</h4>
                <p class="text-muted mb-0">Total Candidates</p>
            </div>
        </div>
    </div>
</div>

<!-- Parties Table -->
<div class="card">
    <div class="card-header bg-light">
        <h5 class="mb-0">
            <i class="bi bi-list"></i> Political Parties List
        </h5>
    </div>
    <div class="card-body">
        @if($parties->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>Party</th>
                            <th>Symbol</th>
                            <th>Contact</th>
                            <th>ECP Registration</th>
                            <th>Candidates</th>
                            <th>Status</th>
                            <th>Founded</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($parties as $party)
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        @if($party->symbol_image)
                                            <img src="{{ $party->symbol_image }}" alt="Symbol" class="me-2" width="32" height="32">
                                        @else
                                            <div class="bg-success text-white rounded d-flex align-items-center justify-content-center me-2" style="width: 32px; height: 32px;">
                                                {{ strtoupper(substr($party->short_name, 0, 1)) }}
                                            </div>
                                        @endif
                                        <div>
                                            <h6 class="mb-0">{{ $party->name['en'] ?? $party->name }}</h6>
                                            <small class="text-muted">{{ $party->short_name }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-light text-dark border">{{ $party->symbol }}</span>
                                </td>
                                <td>
                                    <div>
                                        @if($party->contact_email)
                                            <small class="d-block">{{ $party->contact_email }}</small>
                                        @endif
                                        @if($party->contact_phone)
                                            <small class="text-muted">{{ $party->contact_phone }}</small>
                                        @endif
                                        @if($party->website)
                                            <small class="text-primary d-block">
                                                <a href="{{ $party->website }}" target="_blank">{{ $party->website }}</a>
                                            </small>
                                        @endif
                                    </div>
                                </td>
                                <td>
                                    @if($party->ecp_registration_number)
                                        <code>{{ $party->ecp_registration_number }}</code>
                                    @else
                                        <span class="text-muted">Not registered</span>
                                    @endif
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ $party->candidates_count ?? 0 }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-{{ $party->is_active ? 'success' : 'secondary' }}">
                                        {{ $party->is_active ? 'Active' : 'Inactive' }}
                                    </span>
                                </td>
                                <td>
                                    @if($party->founded_date)
                                        <small>{{ $party->founded_date->format('Y') }}</small>
                                    @else
                                        <span class="text-muted">Unknown</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{{ route('admin.parties.edit', $party) }}" class="btn btn-outline-primary" title="Edit">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <button type="button" class="btn btn-outline-info" title="View Candidates">
                                            <i class="bi bi-people"></i>
                                        </button>
                                        <form method="POST" action="{{ route('admin.parties.delete', $party) }}" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this political party?')">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-outline-danger" title="Delete">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-4">
                {{ $parties->links() }}
            </div>
        @else
            <div class="text-center py-5">
                <i class="bi bi-flag display-1 text-muted"></i>
                <h4 class="text-muted mt-3">No Political Parties Found</h4>
                <p class="text-muted">Start by adding your first political party.</p>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addPartyModal">
                    <i class="bi bi-plus"></i> Add Party
                </button>
            </div>
        @endif
    </div>
</div>

<!-- Add Party Modal -->
<div class="modal fade" id="addPartyModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Political Party</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ route('admin.parties.store') }}">
                @csrf
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name_en" class="form-label">Party Name (English)</label>
                                <input type="text" class="form-control" id="name_en" name="name_en" required>
                            </div>
                            <div class="mb-3">
                                <label for="name_ur" class="form-label">Party Name (Urdu)</label>
                                <input type="text" class="form-control urdu-text" id="name_ur" name="name_ur" dir="rtl">
                            </div>
                            <div class="mb-3">
                                <label for="short_name" class="form-label">Short Name</label>
                                <input type="text" class="form-control" id="short_name" name="short_name" required>
                            </div>
                            <div class="mb-3">
                                <label for="symbol" class="form-label">Election Symbol</label>
                                <input type="text" class="form-control" id="symbol" name="symbol" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="contact_email" class="form-label">Contact Email</label>
                                <input type="email" class="form-control" id="contact_email" name="contact_email">
                            </div>
                            <div class="mb-3">
                                <label for="contact_phone" class="form-label">Contact Phone</label>
                                <input type="tel" class="form-control" id="contact_phone" name="contact_phone">
                            </div>
                            <div class="mb-3">
                                <label for="website" class="form-label">Website</label>
                                <input type="url" class="form-control" id="website" name="website">
                            </div>
                            <div class="mb-3">
                                <label for="ecp_registration_number" class="form-label">ECP Registration Number</label>
                                <input type="text" class="form-control" id="ecp_registration_number" name="ecp_registration_number">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="party_head" class="form-label">Party Head</label>
                        <input type="text" class="form-control" id="party_head" name="party_head">
                    </div>
                    <div class="mb-3">
                        <label for="headquarters" class="form-label">Headquarters</label>
                        <input type="text" class="form-control" id="headquarters" name="headquarters">
                    </div>
                    <div class="mb-3">
                        <label for="founded_date" class="form-label">Founded Date</label>
                        <input type="date" class="form-control" id="founded_date" name="founded_date">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Party</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .table th {
        border-top: none;
        font-weight: 600;
    }
    .card {
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .urdu-text {
        font-family: 'Noto Nastaliq Urdu', serif;
    }
</style>
@endpush
