/**
 * @license
 * MIT License
 * 
 * Copyright (c) 2014-present, <PERSON> and other contributors.
 * 
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 * 
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 * 
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).Immutable={})}(this,(function(t){"use strict";var e="delete",r=5,n=1<<r,i=n-1,o={};function u(t){t&&(t.value=!0)}function s(){}function a(t){return void 0===t.size&&(t.size=t.__iterate(f)),t.size}function c(t,e){if("number"!=typeof e){var r=e>>>0;if(""+r!==e||4294967295===r)return NaN;e=r}return e<0?a(t)+e:e}function f(){return!0}function h(t,e,r){return(0===t&&!v(t)||void 0!==r&&t<=-r)&&(void 0===e||void 0!==r&&e>=r)}function p(t,e){return l(t,e,0)}function _(t,e){return l(t,e,e)}function l(t,e,r){return void 0===t?r:v(t)?e===1/0?e:0|Math.max(0,e+t):void 0===e||e===t?t:0|Math.min(e,t)}function v(t){return t<0||0===t&&1/t==-1/0}var y="@@__IMMUTABLE_ITERABLE__@@";function d(t){return Boolean(t&&t[y])}var g="@@__IMMUTABLE_KEYED__@@";function w(t){return Boolean(t&&t[g])}var m="@@__IMMUTABLE_INDEXED__@@";function b(t){return Boolean(t&&t[m])}function z(t){return w(t)||b(t)}var S=function(t){return d(t)?t:X(t)},I=function(t){function e(t){return w(t)?t:F(t)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e}(S),O=function(t){function e(t){return b(t)?t:G(t)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e}(S),E=function(t){function e(t){return d(t)&&!z(t)?t:Z(t)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e}(S);S.Keyed=I,S.Indexed=O,S.Set=E;var j="@@__IMMUTABLE_SEQ__@@";function q(t){return Boolean(t&&t[j])}var M="@@__IMMUTABLE_RECORD__@@";function D(t){return Boolean(t&&t[M])}function x(t){return d(t)||D(t)}var A="@@__IMMUTABLE_ORDERED__@@";function k(t){return Boolean(t&&t[A])}var R=0,U=1,T=2,B="function"==typeof Symbol&&Symbol.iterator,K="@@iterator",L=B||K,C=function(t){this.next=t};function P(t,e,r,n){var i=t===R?e:t===U?r:[e,r];return n?n.value=i:n={value:i,done:!1},n}function W(){return{value:void 0,done:!0}}function N(t){return!!Array.isArray(t)||!!V(t)}function H(t){return t&&"function"==typeof t.next}function J(t){var e=V(t);return e&&e.call(t)}function V(t){var e=t&&(B&&t[B]||t[K]);if("function"==typeof e)return e}C.prototype.toString=function(){return"[Iterator]"},C.KEYS=R,C.VALUES=U,C.ENTRIES=T,C.prototype.inspect=C.prototype.toSource=function(){return this.toString()},C.prototype[L]=function(){return this};var Y=Object.prototype.hasOwnProperty;function Q(t){return!(!Array.isArray(t)&&"string"!=typeof t)||t&&"object"==typeof t&&Number.isInteger(t.length)&&t.length>=0&&(0===t.length?1===Object.keys(t).length:t.hasOwnProperty(t.length-1))}var X=function(t){function e(t){return null==t?nt():x(t)?t.toSeq():function(t){var e=ut(t);if(e)return(n=V(r=t))&&n===r.entries?e.fromEntrySeq():function(t){var e=V(t);return e&&e===t.keys}(t)?e.toSetSeq():e;var r,n;if("object"==typeof t)return new tt(t);throw new TypeError("Expected Array or collection object of values, or keyed object: "+t)}(t)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.toSeq=function(){return this},e.prototype.toString=function(){return this.__toString("Seq {","}")},e.prototype.cacheResult=function(){return!this._cache&&this.__iterateUncached&&(this._cache=this.entrySeq().toArray(),this.size=this._cache.length),this},e.prototype.__iterate=function(t,e){var r=this._cache;if(r){for(var n=r.length,i=0;i!==n;){var o=r[e?n-++i:i++];if(!1===t(o[1],o[0],this))break}return i}return this.__iterateUncached(t,e)},e.prototype.__iterator=function(t,e){var r=this._cache;if(r){var n=r.length,i=0;return new C((function(){if(i===n)return{value:void 0,done:!0};var o=r[e?n-++i:i++];return P(t,o[0],o[1])}))}return this.__iteratorUncached(t,e)},e}(S),F=function(t){function e(t){return null==t?nt().toKeyedSeq():d(t)?w(t)?t.toSeq():t.fromEntrySeq():D(t)?t.toSeq():it(t)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.toKeyedSeq=function(){return this},e}(X),G=function(t){function e(t){return null==t?nt():d(t)?w(t)?t.entrySeq():t.toIndexedSeq():D(t)?t.toSeq().entrySeq():ot(t)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.of=function(){return e(arguments)},e.prototype.toIndexedSeq=function(){return this},e.prototype.toString=function(){return this.__toString("Seq [","]")},e}(X),Z=function(t){function e(t){return(d(t)&&!z(t)?t:G(t)).toSetSeq()}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.of=function(){return e(arguments)},e.prototype.toSetSeq=function(){return this},e}(X);X.isSeq=q,X.Keyed=F,X.Set=Z,X.Indexed=G,X.prototype[j]=!0;var $=function(t){function e(t){this._array=t,this.size=t.length}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.get=function(t,e){return this.has(t)?this._array[c(this,t)]:e},e.prototype.__iterate=function(t,e){for(var r=this._array,n=r.length,i=0;i!==n;){var o=e?n-++i:i++;if(!1===t(r[o],o,this))break}return i},e.prototype.__iterator=function(t,e){var r=this._array,n=r.length,i=0;return new C((function(){if(i===n)return{value:void 0,done:!0};var o=e?n-++i:i++;return P(t,o,r[o])}))},e}(G),tt=function(t){function e(t){var e=Object.keys(t).concat(Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(t):[]);this._object=t,this._keys=e,this.size=e.length}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.get=function(t,e){return void 0===e||this.has(t)?this._object[t]:e},e.prototype.has=function(t){return Y.call(this._object,t)},e.prototype.__iterate=function(t,e){for(var r=this._object,n=this._keys,i=n.length,o=0;o!==i;){var u=n[e?i-++o:o++];if(!1===t(r[u],u,this))break}return o},e.prototype.__iterator=function(t,e){var r=this._object,n=this._keys,i=n.length,o=0;return new C((function(){if(o===i)return{value:void 0,done:!0};var u=n[e?i-++o:o++];return P(t,u,r[u])}))},e}(F);tt.prototype[A]=!0;var et,rt=function(t){function e(t){this._collection=t,this.size=t.length||t.size}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.__iterateUncached=function(t,e){if(e)return this.cacheResult().__iterate(t,e);var r=J(this._collection),n=0;if(H(r))for(var i;!(i=r.next()).done&&!1!==t(i.value,n++,this););return n},e.prototype.__iteratorUncached=function(t,e){if(e)return this.cacheResult().__iterator(t,e);var r=J(this._collection);if(!H(r))return new C(W);var n=0;return new C((function(){var e=r.next();return e.done?e:P(t,n++,e.value)}))},e}(G);function nt(){return et||(et=new $([]))}function it(t){var e=ut(t);if(e)return e.fromEntrySeq();if("object"==typeof t)return new tt(t);throw new TypeError("Expected Array or collection object of [k, v] entries, or keyed object: "+t)}function ot(t){var e=ut(t);if(e)return e;throw new TypeError("Expected Array or collection object of values: "+t)}function ut(t){return Q(t)?new $(t):N(t)?new rt(t):void 0}var st="@@__IMMUTABLE_MAP__@@";function at(t){return Boolean(t&&t[st])}function ct(t){return at(t)&&k(t)}function ft(t){return Boolean(t&&"function"==typeof t.equals&&"function"==typeof t.hashCode)}function ht(t,e){if(t===e||t!=t&&e!=e)return!0;if(!t||!e)return!1;if("function"==typeof t.valueOf&&"function"==typeof e.valueOf){if((t=t.valueOf())===(e=e.valueOf())||t!=t&&e!=e)return!0;if(!t||!e)return!1}return!!(ft(t)&&ft(e)&&t.equals(e))}var pt="function"==typeof Math.imul&&-2===Math.imul(4294967295,2)?Math.imul:function(t,e){var r=65535&(t|=0),n=65535&(e|=0);return r*n+((t>>>16)*n+r*(e>>>16)<<16>>>0)|0};function _t(t){return t>>>1&1073741824|3221225471&t}var lt=Object.prototype.valueOf;function vt(t){if(null==t)return yt(t);if("function"==typeof t.hashCode)return _t(t.hashCode(t));var e,r=(e=t).valueOf!==lt&&"function"==typeof e.valueOf?e.valueOf(e):e;if(null==r)return yt(r);switch(typeof r){case"boolean":return r?1108378657:1108378656;case"number":return function(t){if(t!=t||t===1/0)return 0;var e=0|t;e!==t&&(e^=4294967295*t);for(;t>4294967295;)e^=t/=4294967295;return _t(e)}(r);case"string":return r.length>Et?function(t){var e=Mt[t];void 0===e&&(e=dt(t),qt===jt&&(qt=0,Mt={}),qt++,Mt[t]=e);return e}(r):dt(r);case"object":case"function":return function(t){var e;if(zt&&void 0!==(e=bt.get(t)))return e;if(e=t[Ot],void 0!==e)return e;if(!wt){if(void 0!==(e=t.propertyIsEnumerable&&t.propertyIsEnumerable[Ot]))return e;if(void 0!==(e=function(t){if(t&&t.nodeType>0)switch(t.nodeType){case 1:return t.uniqueID;case 9:return t.documentElement&&t.documentElement.uniqueID}}(t)))return e}if(e=mt(),zt)bt.set(t,e);else{if(void 0!==gt&&!1===gt(t))throw new Error("Non-extensible objects are not allowed as keys.");if(wt)Object.defineProperty(t,Ot,{enumerable:!1,configurable:!1,writable:!1,value:e});else if(void 0!==t.propertyIsEnumerable&&t.propertyIsEnumerable===t.constructor.prototype.propertyIsEnumerable)t.propertyIsEnumerable=function(){return this.constructor.prototype.propertyIsEnumerable.apply(this,arguments)},t.propertyIsEnumerable[Ot]=e;else{if(void 0===t.nodeType)throw new Error("Unable to set a non-enumerable property on object.");t[Ot]=e}}return e}(r);case"symbol":return function(t){var e=St[t];if(void 0!==e)return e;return e=mt(),St[t]=e,e}(r);default:if("function"==typeof r.toString)return dt(r.toString());throw new Error("Value type "+typeof r+" cannot be hashed.")}}function yt(t){return null===t?1108378658:1108378659}function dt(t){for(var e=0,r=0;r<t.length;r++)e=31*e+t.charCodeAt(r)|0;return _t(e)}var gt=Object.isExtensible,wt=function(){try{return Object.defineProperty({},"@",{}),!0}catch(t){return!1}}();function mt(){var t=++It;return 1073741824&It&&(It=0),t}var bt,zt="function"==typeof WeakMap;zt&&(bt=new WeakMap);var St=Object.create(null),It=0,Ot="__immutablehash__";"function"==typeof Symbol&&(Ot=Symbol(Ot));var Et=16,jt=255,qt=0,Mt={},Dt=function(t){function e(t,e){this._iter=t,this._useKeys=e,this.size=t.size}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.get=function(t,e){return this._iter.get(t,e)},e.prototype.has=function(t){return this._iter.has(t)},e.prototype.valueSeq=function(){return this._iter.valueSeq()},e.prototype.reverse=function(){var t=this,e=Tt(this,!0);return this._useKeys||(e.valueSeq=function(){return t._iter.toSeq().reverse()}),e},e.prototype.map=function(t,e){var r=this,n=Ut(this,t,e);return this._useKeys||(n.valueSeq=function(){return r._iter.toSeq().map(t,e)}),n},e.prototype.__iterate=function(t,e){var r=this;return this._iter.__iterate((function(e,n){return t(e,n,r)}),e)},e.prototype.__iterator=function(t,e){return this._iter.__iterator(t,e)},e}(F);Dt.prototype[A]=!0;var xt=function(t){function e(t){this._iter=t,this.size=t.size}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.includes=function(t){return this._iter.includes(t)},e.prototype.__iterate=function(t,e){var r=this,n=0;return e&&a(this),this._iter.__iterate((function(i){return t(i,e?r.size-++n:n++,r)}),e)},e.prototype.__iterator=function(t,e){var r=this,n=this._iter.__iterator(U,e),i=0;return e&&a(this),new C((function(){var o=n.next();return o.done?o:P(t,e?r.size-++i:i++,o.value,o)}))},e}(G),At=function(t){function e(t){this._iter=t,this.size=t.size}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.has=function(t){return this._iter.includes(t)},e.prototype.__iterate=function(t,e){var r=this;return this._iter.__iterate((function(e){return t(e,e,r)}),e)},e.prototype.__iterator=function(t,e){var r=this._iter.__iterator(U,e);return new C((function(){var e=r.next();return e.done?e:P(t,e.value,e.value,e)}))},e}(Z),kt=function(t){function e(t){this._iter=t,this.size=t.size}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.entrySeq=function(){return this._iter.toSeq()},e.prototype.__iterate=function(t,e){var r=this;return this._iter.__iterate((function(e){if(e){Yt(e);var n=d(e);return t(n?e.get(1):e[1],n?e.get(0):e[0],r)}}),e)},e.prototype.__iterator=function(t,e){var r=this._iter.__iterator(U,e);return new C((function(){for(;;){var e=r.next();if(e.done)return e;var n=e.value;if(n){Yt(n);var i=d(n);return P(t,i?n.get(0):n[0],i?n.get(1):n[1],e)}}}))},e}(F);function Rt(t){var e=Xt(t);return e._iter=t,e.size=t.size,e.flip=function(){return t},e.reverse=function(){var e=t.reverse.apply(this);return e.flip=function(){return t.reverse()},e},e.has=function(e){return t.includes(e)},e.includes=function(e){return t.has(e)},e.cacheResult=Ft,e.__iterateUncached=function(e,r){var n=this;return t.__iterate((function(t,r){return!1!==e(r,t,n)}),r)},e.__iteratorUncached=function(e,r){if(e===T){var n=t.__iterator(e,r);return new C((function(){var t=n.next();if(!t.done){var e=t.value[0];t.value[0]=t.value[1],t.value[1]=e}return t}))}return t.__iterator(e===U?R:U,r)},e}function Ut(t,e,r){var n=Xt(t);return n.size=t.size,n.has=function(e){return t.has(e)},n.get=function(n,i){var u=t.get(n,o);return u===o?i:e.call(r,u,n,t)},n.__iterateUncached=function(n,i){var o=this;return t.__iterate((function(t,i,u){return!1!==n(e.call(r,t,i,u),i,o)}),i)},n.__iteratorUncached=function(n,i){var o=t.__iterator(T,i);return new C((function(){var i=o.next();if(i.done)return i;var u=i.value,s=u[0];return P(n,s,e.call(r,u[1],s,t),i)}))},n}function Tt(t,e){var r=this,n=Xt(t);return n._iter=t,n.size=t.size,n.reverse=function(){return t},t.flip&&(n.flip=function(){var e=Rt(t);return e.reverse=function(){return t.flip()},e}),n.get=function(r,n){return t.get(e?r:-1-r,n)},n.has=function(r){return t.has(e?r:-1-r)},n.includes=function(e){return t.includes(e)},n.cacheResult=Ft,n.__iterate=function(r,n){var i=this,o=0;return n&&a(t),t.__iterate((function(t,u){return r(t,e?u:n?i.size-++o:o++,i)}),!n)},n.__iterator=function(n,i){var o=0;i&&a(t);var u=t.__iterator(T,!i);return new C((function(){var t=u.next();if(t.done)return t;var s=t.value;return P(n,e?s[0]:i?r.size-++o:o++,s[1],t)}))},n}function Bt(t,e,r,n){var i=Xt(t);return n&&(i.has=function(n){var i=t.get(n,o);return i!==o&&!!e.call(r,i,n,t)},i.get=function(n,i){var u=t.get(n,o);return u!==o&&e.call(r,u,n,t)?u:i}),i.__iterateUncached=function(i,o){var u=this,s=0;return t.__iterate((function(t,o,a){if(e.call(r,t,o,a))return s++,i(t,n?o:s-1,u)}),o),s},i.__iteratorUncached=function(i,o){var u=t.__iterator(T,o),s=0;return new C((function(){for(;;){var o=u.next();if(o.done)return o;var a=o.value,c=a[0],f=a[1];if(e.call(r,f,c,t))return P(i,n?c:s++,f,o)}}))},i}function Kt(t,e,r,n){var i=t.size;if(h(e,r,i))return t;if(void 0===i&&(e<0||r<0))return Kt(t.toSeq().cacheResult(),e,r,n);var o,u=p(e,i),s=_(r,i)-u;s==s&&(o=s<0?0:s);var a=Xt(t);return a.size=0===o?o:t.size&&o||void 0,!n&&q(t)&&o>=0&&(a.get=function(e,r){return(e=c(this,e))>=0&&e<o?t.get(e+u,r):r}),a.__iterateUncached=function(e,r){var i=this;if(0===o)return 0;if(r)return this.cacheResult().__iterate(e,r);var s=0,a=!0,c=0;return t.__iterate((function(t,r){if(!a||!(a=s++<u))return c++,!1!==e(t,n?r:c-1,i)&&c!==o})),c},a.__iteratorUncached=function(e,r){if(0!==o&&r)return this.cacheResult().__iterator(e,r);if(0===o)return new C(W);var i=t.__iterator(e,r),s=0,a=0;return new C((function(){for(;s++<u;)i.next();if(++a>o)return{value:void 0,done:!0};var t=i.next();return n||e===U||t.done?t:P(e,a-1,e===R?void 0:t.value[1],t)}))},a}function Lt(t,e,r,n){var i=Xt(t);return i.__iterateUncached=function(i,o){var u=this;if(o)return this.cacheResult().__iterate(i,o);var s=!0,a=0;return t.__iterate((function(t,o,c){if(!s||!(s=e.call(r,t,o,c)))return a++,i(t,n?o:a-1,u)})),a},i.__iteratorUncached=function(i,o){var u=this;if(o)return this.cacheResult().__iterator(i,o);var s=t.__iterator(T,o),a=!0,c=0;return new C((function(){var t,o,f;do{if((t=s.next()).done)return n||i===U?t:P(i,c++,i===R?void 0:t.value[1],t);var h=t.value;o=h[0],f=h[1],a&&(a=e.call(r,f,o,u))}while(a);return i===T?t:P(i,o,f,t)}))},i}xt.prototype.cacheResult=Dt.prototype.cacheResult=At.prototype.cacheResult=kt.prototype.cacheResult=Ft;var Ct=function(t){function e(t){this._wrappedIterables=t.flatMap((function(t){return t._wrappedIterables?t._wrappedIterables:[t]})),this.size=this._wrappedIterables.reduce((function(t,e){if(void 0!==t){var r=e.size;if(void 0!==r)return t+r}}),0),this[g]=this._wrappedIterables[0][g],this[m]=this._wrappedIterables[0][m],this[A]=this._wrappedIterables[0][A]}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.__iterateUncached=function(t,e){if(0!==this._wrappedIterables.length){if(e)return this.cacheResult().__iterate(t,e);for(var r=0,n=w(this),i=n?T:U,o=this._wrappedIterables[r].__iterator(i,e),u=!0,s=0;u;){for(var a=o.next();a.done;){if(++r===this._wrappedIterables.length)return s;a=(o=this._wrappedIterables[r].__iterator(i,e)).next()}u=!1!==(n?t(a.value[1],a.value[0],this):t(a.value,s,this)),s++}return s}},e.prototype.__iteratorUncached=function(t,e){var r=this;if(0===this._wrappedIterables.length)return new C(W);if(e)return this.cacheResult().__iterator(t,e);var n=0,i=this._wrappedIterables[n].__iterator(t,e);return new C((function(){for(var o=i.next();o.done;){if(++n===r._wrappedIterables.length)return o;o=(i=r._wrappedIterables[n].__iterator(t,e)).next()}return o}))},e}(X);function Pt(t,e,r){var n=Xt(t);return n.__iterateUncached=function(i,o){if(o)return this.cacheResult().__iterate(i,o);var u=0,s=!1;return function t(a,c){a.__iterate((function(o,a){return(!e||c<e)&&d(o)?t(o,c+1):(u++,!1===i(o,r?a:u-1,n)&&(s=!0)),!s}),o)}(t,0),u},n.__iteratorUncached=function(n,i){if(i)return this.cacheResult().__iterator(n,i);var o=t.__iterator(n,i),u=[],s=0;return new C((function(){for(;o;){var t=o.next();if(!1===t.done){var a=t.value;if(n===T&&(a=a[1]),e&&!(u.length<e)||!d(a))return r?t:P(n,s++,a,t);u.push(o),o=a.__iterator(n,i)}else o=u.pop()}return{value:void 0,done:!0}}))},n}function Wt(t,e,r){e||(e=Gt);var n=w(t),i=0,o=t.toSeq().map((function(e,n){return[n,e,i++,r?r(e,n,t):e]})).valueSeq().toArray();return o.sort((function(t,r){return e(t[3],r[3])||t[2]-r[2]})).forEach(n?function(t,e){o[e].length=2}:function(t,e){o[e]=t[1]}),n?F(o):b(t)?G(o):Z(o)}function Nt(t,e,r){if(e||(e=Gt),r){var n=t.toSeq().map((function(e,n){return[e,r(e,n,t)]})).reduce((function(t,r){return Ht(e,t[1],r[1])?r:t}));return n&&n[0]}return t.reduce((function(t,r){return Ht(e,t,r)?r:t}))}function Ht(t,e,r){var n=t(r,e);return 0===n&&r!==e&&(null==r||r!=r)||n>0}function Jt(t,e,r,n){var i=Xt(t),o=new $(r).map((function(t){return t.size}));return i.size=n?o.max():o.min(),i.__iterate=function(t,e){for(var r,n=this.__iterator(U,e),i=0;!(r=n.next()).done&&!1!==t(r.value,i++,this););return i},i.__iteratorUncached=function(t,i){var o=r.map((function(t){return t=S(t),J(i?t.reverse():t)})),u=0,s=!1;return new C((function(){var r;return s||(r=o.map((function(t){return t.next()})),s=n?r.every((function(t){return t.done})):r.some((function(t){return t.done}))),s?{value:void 0,done:!0}:P(t,u++,e.apply(null,r.map((function(t){return t.value}))))}))},i}function Vt(t,e){return t===e?t:q(t)?e:t.constructor(e)}function Yt(t){if(t!==Object(t))throw new TypeError("Expected [K, V] tuple: "+t)}function Qt(t){return w(t)?I:b(t)?O:E}function Xt(t){return Object.create((w(t)?F:b(t)?G:Z).prototype)}function Ft(){return this._iter.cacheResult?(this._iter.cacheResult(),this.size=this._iter.size,this):X.prototype.cacheResult.call(this)}function Gt(t,e){return void 0===t&&void 0===e?0:void 0===t?1:void 0===e?-1:t>e?1:t<e?-1:0}function Zt(t,e){e=e||0;for(var r=Math.max(0,t.length-e),n=new Array(r),i=0;i<r;i++)n[i]=t[i+e];return n}function $t(t,e){if(!t)throw new Error(e)}function te(t){$t(t!==1/0,"Cannot perform this action with an infinite size.")}function ee(t){if(Q(t)&&"string"!=typeof t)return t;if(k(t))return t.toArray();throw new TypeError("Invalid keyPath: expected Ordered Collection or Array: "+t)}var re=Object.prototype.toString;function ne(t){if(!t||"object"!=typeof t||"[object Object]"!==re.call(t))return!1;var e=Object.getPrototypeOf(t);if(null===e)return!0;for(var r=e,n=Object.getPrototypeOf(e);null!==n;)r=n,n=Object.getPrototypeOf(r);return r===e}function ie(t){return"object"==typeof t&&(x(t)||Array.isArray(t)||ne(t))}function oe(t){try{return"string"==typeof t?JSON.stringify(t):String(t)}catch(e){return JSON.stringify(t)}}function ue(t,e){return x(t)?t.has(e):ie(t)&&Y.call(t,e)}function se(t,e,r){return x(t)?t.get(e,r):ue(t,e)?"function"==typeof t.get?t.get(e):t[e]:r}function ae(t){if(Array.isArray(t))return Zt(t);var e={};for(var r in t)Y.call(t,r)&&(e[r]=t[r]);return e}function ce(t,e){if(!ie(t))throw new TypeError("Cannot update non-data-structure value: "+t);if(x(t)){if(!t.remove)throw new TypeError("Cannot update immutable value without .remove() method: "+t);return t.remove(e)}if(!Y.call(t,e))return t;var r=ae(t);return Array.isArray(r)?r.splice(e,1):delete r[e],r}function fe(t,e,r){if(!ie(t))throw new TypeError("Cannot update non-data-structure value: "+t);if(x(t)){if(!t.set)throw new TypeError("Cannot update immutable value without .set() method: "+t);return t.set(e,r)}if(Y.call(t,e)&&r===t[e])return t;var n=ae(t);return n[e]=r,n}function he(t,e,r,n){n||(n=r,r=void 0);var i=pe(x(t),t,ee(e),0,r,n);return i===o?r:i}function pe(t,e,r,n,i,u){var s=e===o;if(n===r.length){var a=s?i:e,c=u(a);return c===a?e:c}if(!s&&!ie(e))throw new TypeError("Cannot update within non-data-structure value in path ["+Array.from(r).slice(0,n).map(oe)+"]: "+e);var f=r[n],h=s?o:se(e,f,o),p=pe(h===o?t:x(h),h,r,n+1,i,u);return p===h?e:p===o?ce(e,f):fe(s?t?Je():{}:e,f,p)}function _e(t,e,r){return he(t,e,o,(function(){return r}))}function le(t,e){return _e(this,t,e)}function ve(t,e){return he(t,e,(function(){return o}))}function ye(t){return ve(this,t)}function de(t,e,r,n){return he(t,[e],r,n)}function ge(t,e,r){return 1===arguments.length?t(this):de(this,t,e,r)}function we(t,e,r){return he(this,t,e,r)}function me(){for(var t=[],e=arguments.length;e--;)t[e]=arguments[e];return ze(this,t)}function be(t){for(var e=[],r=arguments.length-1;r-- >0;)e[r]=arguments[r+1];if("function"!=typeof t)throw new TypeError("Invalid merger function: "+t);return ze(this,e,t)}function ze(t,e,r){for(var n=[],i=0;i<e.length;i++){var u=I(e[i]);0!==u.size&&n.push(u)}return 0===n.length?t:0!==t.toSeq().size||t.__ownerID||1!==n.length?t.withMutations((function(t){for(var e=r?function(e,n){de(t,n,o,(function(t){return t===o?e:r(t,e,n)}))}:function(e,r){t.set(r,e)},i=0;i<n.length;i++)n[i].forEach(e)})):D(t)?t:t.constructor(n[0])}function Se(t,e,r){return Ie(t,e,function(t){function e(r,n,i){return ie(r)&&ie(n)&&(o=n,u=X(r),s=X(o),b(u)===b(s)&&w(u)===w(s))?Ie(r,[n],e):t?t(r,n,i):n;var o,u,s}return e}(r))}function Ie(t,e,r){if(!ie(t))throw new TypeError("Cannot merge into non-data-structure value: "+t);if(x(t))return"function"==typeof r&&t.mergeWith?t.mergeWith.apply(t,[r].concat(e)):t.merge?t.merge.apply(t,e):t.concat.apply(t,e);for(var n=Array.isArray(t),i=t,o=n?O:I,u=n?function(e){i===t&&(i=ae(i)),i.push(e)}:function(e,n){var o=Y.call(i,n),u=o&&r?r(i[n],e,n):e;o&&u===i[n]||(i===t&&(i=ae(i)),i[n]=u)},s=0;s<e.length;s++)o(e[s]).forEach(u);return i}function Oe(){for(var t=[],e=arguments.length;e--;)t[e]=arguments[e];return Se(this,t)}function Ee(t){for(var e=[],r=arguments.length-1;r-- >0;)e[r]=arguments[r+1];return Se(this,e,t)}function je(t){for(var e=[],r=arguments.length-1;r-- >0;)e[r]=arguments[r+1];return he(this,t,Je(),(function(t){return Ie(t,e)}))}function qe(t){for(var e=[],r=arguments.length-1;r-- >0;)e[r]=arguments[r+1];return he(this,t,Je(),(function(t){return Se(t,e)}))}function Me(t){var e=this.asMutable();return t(e),e.wasAltered()?e.__ensureOwner(this.__ownerID):this}function De(){return this.__ownerID?this:this.__ensureOwner(new s)}function xe(){return this.__ensureOwner()}function Ae(){return this.__altered}var ke=function(t){function e(e){return null==e?Je():at(e)&&!k(e)?e:Je().withMutations((function(r){var n=t(e);te(n.size),n.forEach((function(t,e){return r.set(e,t)}))}))}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.toString=function(){return this.__toString("Map {","}")},e.prototype.get=function(t,e){return this._root?this._root.get(0,void 0,t,e):e},e.prototype.set=function(t,e){return Ve(this,t,e)},e.prototype.remove=function(t){return Ve(this,t,o)},e.prototype.deleteAll=function(t){var e=S(t);return 0===e.size?this:this.withMutations((function(t){e.forEach((function(e){return t.remove(e)}))}))},e.prototype.clear=function(){return 0===this.size?this:this.__ownerID?(this.size=0,this._root=null,this.__hash=void 0,this.__altered=!0,this):Je()},e.prototype.sort=function(t){return yr(Wt(this,t))},e.prototype.sortBy=function(t,e){return yr(Wt(this,e,t))},e.prototype.map=function(t,e){var r=this;return this.withMutations((function(n){n.forEach((function(i,o){n.set(o,t.call(e,i,o,r))}))}))},e.prototype.__iterator=function(t,e){return new Pe(this,t,e)},e.prototype.__iterate=function(t,e){var r=this,n=0;return this._root&&this._root.iterate((function(e){return n++,t(e[1],e[0],r)}),e),n},e.prototype.__ensureOwner=function(t){return t===this.__ownerID?this:t?He(this.size,this._root,t,this.__hash):0===this.size?Je():(this.__ownerID=t,this.__altered=!1,this)},e}(I);ke.isMap=at;var Re=ke.prototype;Re[st]=!0,Re[e]=Re.remove,Re.removeAll=Re.deleteAll,Re.setIn=le,Re.removeIn=Re.deleteIn=ye,Re.update=ge,Re.updateIn=we,Re.merge=Re.concat=me,Re.mergeWith=be,Re.mergeDeep=Oe,Re.mergeDeepWith=Ee,Re.mergeIn=je,Re.mergeDeepIn=qe,Re.withMutations=Me,Re.wasAltered=Ae,Re.asImmutable=xe,Re["@@transducer/init"]=Re.asMutable=De,Re["@@transducer/step"]=function(t,e){return t.set(e[0],e[1])},Re["@@transducer/result"]=function(t){return t.asImmutable()};var Ue=function(t,e){this.ownerID=t,this.entries=e};Ue.prototype.get=function(t,e,r,n){for(var i=this.entries,o=0,u=i.length;o<u;o++)if(ht(r,i[o][0]))return i[o][1];return n},Ue.prototype.update=function(t,e,r,n,i,a,c){for(var f=i===o,h=this.entries,p=0,_=h.length;p<_&&!ht(n,h[p][0]);p++);var l=p<_;if(l?h[p][1]===i:f)return this;if(u(c),(f||!l)&&u(a),!f||1!==h.length){if(!l&&!f&&h.length>=Ze)return function(t,e,r,n){t||(t=new s);for(var i=new Le(t,vt(r),[r,n]),o=0;o<e.length;o++){var u=e[o];i=i.update(t,0,void 0,u[0],u[1])}return i}(t,h,n,i);var v=t&&t===this.ownerID,y=v?h:Zt(h);return l?f?p===_-1?y.pop():y[p]=y.pop():y[p]=[n,i]:y.push([n,i]),v?(this.entries=y,this):new Ue(t,y)}};var Te=function(t,e,r){this.ownerID=t,this.bitmap=e,this.nodes=r};Te.prototype.get=function(t,e,n,o){void 0===e&&(e=vt(n));var u=1<<((0===t?e:e>>>t)&i),s=this.bitmap;return 0==(s&u)?o:this.nodes[Fe(s&u-1)].get(t+r,e,n,o)},Te.prototype.update=function(t,e,u,s,a,c,f){void 0===u&&(u=vt(s));var h=(0===e?u:u>>>e)&i,p=1<<h,_=this.bitmap,l=0!=(_&p);if(!l&&a===o)return this;var v=Fe(_&p-1),y=this.nodes,d=l?y[v]:void 0,g=Ye(d,t,e+r,u,s,a,c,f);if(g===d)return this;if(!l&&g&&y.length>=$e)return function(t,e,r,i,o){for(var u=0,s=new Array(n),a=0;0!==r;a++,r>>>=1)s[a]=1&r?e[u++]:void 0;return s[i]=o,new Be(t,u+1,s)}(t,y,_,h,g);if(l&&!g&&2===y.length&&Qe(y[1^v]))return y[1^v];if(l&&g&&1===y.length&&Qe(g))return g;var w=t&&t===this.ownerID,m=l?g?_:_^p:_|p,b=l?g?Ge(y,v,g,w):function(t,e,r){var n=t.length-1;if(r&&e===n)return t.pop(),t;for(var i=new Array(n),o=0,u=0;u<n;u++)u===e&&(o=1),i[u]=t[u+o];return i}(y,v,w):function(t,e,r,n){var i=t.length+1;if(n&&e+1===i)return t[e]=r,t;for(var o=new Array(i),u=0,s=0;s<i;s++)s===e?(o[s]=r,u=-1):o[s]=t[s+u];return o}(y,v,g,w);return w?(this.bitmap=m,this.nodes=b,this):new Te(t,m,b)};var Be=function(t,e,r){this.ownerID=t,this.count=e,this.nodes=r};Be.prototype.get=function(t,e,n,o){void 0===e&&(e=vt(n));var u=(0===t?e:e>>>t)&i,s=this.nodes[u];return s?s.get(t+r,e,n,o):o},Be.prototype.update=function(t,e,n,u,s,a,c){void 0===n&&(n=vt(u));var f=(0===e?n:n>>>e)&i,h=s===o,p=this.nodes,_=p[f];if(h&&!_)return this;var l=Ye(_,t,e+r,n,u,s,a,c);if(l===_)return this;var v=this.count;if(_){if(!l&&--v<tr)return function(t,e,r,n){for(var i=0,o=0,u=new Array(r),s=0,a=1,c=e.length;s<c;s++,a<<=1){var f=e[s];void 0!==f&&s!==n&&(i|=a,u[o++]=f)}return new Te(t,i,u)}(t,p,v,f)}else v++;var y=t&&t===this.ownerID,d=Ge(p,f,l,y);return y?(this.count=v,this.nodes=d,this):new Be(t,v,d)};var Ke=function(t,e,r){this.ownerID=t,this.keyHash=e,this.entries=r};Ke.prototype.get=function(t,e,r,n){for(var i=this.entries,o=0,u=i.length;o<u;o++)if(ht(r,i[o][0]))return i[o][1];return n},Ke.prototype.update=function(t,e,r,n,i,s,a){void 0===r&&(r=vt(n));var c=i===o;if(r!==this.keyHash)return c?this:(u(a),u(s),Xe(this,t,e,r,[n,i]));for(var f=this.entries,h=0,p=f.length;h<p&&!ht(n,f[h][0]);h++);var _=h<p;if(_?f[h][1]===i:c)return this;if(u(a),(c||!_)&&u(s),c&&2===p)return new Le(t,this.keyHash,f[1^h]);var l=t&&t===this.ownerID,v=l?f:Zt(f);return _?c?h===p-1?v.pop():v[h]=v.pop():v[h]=[n,i]:v.push([n,i]),l?(this.entries=v,this):new Ke(t,this.keyHash,v)};var Le=function(t,e,r){this.ownerID=t,this.keyHash=e,this.entry=r};Le.prototype.get=function(t,e,r,n){return ht(r,this.entry[0])?this.entry[1]:n},Le.prototype.update=function(t,e,r,n,i,s,a){var c=i===o,f=ht(n,this.entry[0]);return(f?i===this.entry[1]:c)?this:(u(a),c?void u(s):f?t&&t===this.ownerID?(this.entry[1]=i,this):new Le(t,this.keyHash,[n,i]):(u(s),Xe(this,t,e,vt(n),[n,i])))},Ue.prototype.iterate=Ke.prototype.iterate=function(t,e){for(var r=this.entries,n=0,i=r.length-1;n<=i;n++)if(!1===t(r[e?i-n:n]))return!1},Te.prototype.iterate=Be.prototype.iterate=function(t,e){for(var r=this.nodes,n=0,i=r.length-1;n<=i;n++){var o=r[e?i-n:n];if(o&&!1===o.iterate(t,e))return!1}},Le.prototype.iterate=function(t,e){return t(this.entry)};var Ce,Pe=function(t){function e(t,e,r){this._type=e,this._reverse=r,this._stack=t._root&&Ne(t._root)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.next=function(){for(var t=this._type,e=this._stack;e;){var r=e.node,n=e.index++,i=void 0;if(r.entry){if(0===n)return We(t,r.entry)}else if(r.entries){if(n<=(i=r.entries.length-1))return We(t,r.entries[this._reverse?i-n:n])}else if(n<=(i=r.nodes.length-1)){var o=r.nodes[this._reverse?i-n:n];if(o){if(o.entry)return We(t,o.entry);e=this._stack=Ne(o,e)}continue}e=this._stack=this._stack.__prev}return{value:void 0,done:!0}},e}(C);function We(t,e){return P(t,e[0],e[1])}function Ne(t,e){return{node:t,index:0,__prev:e}}function He(t,e,r,n){var i=Object.create(Re);return i.size=t,i._root=e,i.__ownerID=r,i.__hash=n,i.__altered=!1,i}function Je(){return Ce||(Ce=He(0))}function Ve(t,e,r){var n,i;if(t._root){var u={value:!1},s={value:!1};if(n=Ye(t._root,t.__ownerID,0,void 0,e,r,u,s),!s.value)return t;i=t.size+(u.value?r===o?-1:1:0)}else{if(r===o)return t;i=1,n=new Ue(t.__ownerID,[[e,r]])}return t.__ownerID?(t.size=i,t._root=n,t.__hash=void 0,t.__altered=!0,t):n?He(i,n):Je()}function Ye(t,e,r,n,i,s,a,c){return t?t.update(e,r,n,i,s,a,c):s===o?t:(u(c),u(a),new Le(e,n,[i,s]))}function Qe(t){return t.constructor===Le||t.constructor===Ke}function Xe(t,e,n,o,u){if(t.keyHash===o)return new Ke(e,o,[t.entry,u]);var s,a=(0===n?t.keyHash:t.keyHash>>>n)&i,c=(0===n?o:o>>>n)&i,f=a===c?[Xe(t,e,n+r,o,u)]:(s=new Le(e,o,u),a<c?[t,s]:[s,t]);return new Te(e,1<<a|1<<c,f)}function Fe(t){return t=(t=(858993459&(t-=t>>1&1431655765))+(t>>2&858993459))+(t>>4)&252645135,t+=t>>8,127&(t+=t>>16)}function Ge(t,e,r,n){var i=n?t:Zt(t);return i[e]=r,i}var Ze=n/4,$e=n/2,tr=n/4,er="@@__IMMUTABLE_LIST__@@";function rr(t){return Boolean(t&&t[er])}var nr=function(t){function e(e){var i=cr();if(null==e)return i;if(rr(e))return e;var o=t(e),u=o.size;return 0===u?i:(te(u),u>0&&u<n?ar(0,u,r,null,new or(o.toArray())):i.withMutations((function(t){t.setSize(u),o.forEach((function(e,r){return t.set(r,e)}))})))}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.of=function(){return this(arguments)},e.prototype.toString=function(){return this.__toString("List [","]")},e.prototype.get=function(t,e){if((t=c(this,t))>=0&&t<this.size){var r=pr(this,t+=this._origin);return r&&r.array[t&i]}return e},e.prototype.set=function(t,e){return function(t,e,r){if(e=c(t,e),e!=e)return t;if(e>=t.size||e<0)return t.withMutations((function(t){e<0?_r(t,e).set(0,r):_r(t,0,e+1).set(e,r)}));e+=t._origin;var n=t._tail,i=t._root,o={value:!1};e>=lr(t._capacity)?n=fr(n,t.__ownerID,0,e,r,o):i=fr(i,t.__ownerID,t._level,e,r,o);if(!o.value)return t;if(t.__ownerID)return t._root=i,t._tail=n,t.__hash=void 0,t.__altered=!0,t;return ar(t._origin,t._capacity,t._level,i,n)}(this,t,e)},e.prototype.remove=function(t){return this.has(t)?0===t?this.shift():t===this.size-1?this.pop():this.splice(t,1):this},e.prototype.insert=function(t,e){return this.splice(t,0,e)},e.prototype.clear=function(){return 0===this.size?this:this.__ownerID?(this.size=this._origin=this._capacity=0,this._level=r,this._root=this._tail=this.__hash=void 0,this.__altered=!0,this):cr()},e.prototype.push=function(){var t=arguments,e=this.size;return this.withMutations((function(r){_r(r,0,e+t.length);for(var n=0;n<t.length;n++)r.set(e+n,t[n])}))},e.prototype.pop=function(){return _r(this,0,-1)},e.prototype.unshift=function(){var t=arguments;return this.withMutations((function(e){_r(e,-t.length);for(var r=0;r<t.length;r++)e.set(r,t[r])}))},e.prototype.shift=function(){return _r(this,1)},e.prototype.shuffle=function(t){return void 0===t&&(t=Math.random),this.withMutations((function(e){for(var r,n,i=e.size;i;)r=Math.floor(t()*i--),n=e.get(r),e.set(r,e.get(i)),e.set(i,n)}))},e.prototype.concat=function(){for(var e=arguments,r=[],n=0;n<arguments.length;n++){var i=e[n],o=t("string"!=typeof i&&N(i)?i:[i]);0!==o.size&&r.push(o)}return 0===r.length?this:0!==this.size||this.__ownerID||1!==r.length?this.withMutations((function(t){r.forEach((function(e){return e.forEach((function(e){return t.push(e)}))}))})):this.constructor(r[0])},e.prototype.setSize=function(t){return _r(this,0,t)},e.prototype.map=function(t,e){var r=this;return this.withMutations((function(n){for(var i=0;i<r.size;i++)n.set(i,t.call(e,n.get(i),i,r))}))},e.prototype.slice=function(t,e){var r=this.size;return h(t,e,r)?this:_r(this,p(t,r),_(e,r))},e.prototype.__iterator=function(t,e){var r=e?this.size:0,n=sr(this,e);return new C((function(){var i=n();return i===ur?{value:void 0,done:!0}:P(t,e?--r:r++,i)}))},e.prototype.__iterate=function(t,e){for(var r,n=e?this.size:0,i=sr(this,e);(r=i())!==ur&&!1!==t(r,e?--n:n++,this););return n},e.prototype.__ensureOwner=function(t){return t===this.__ownerID?this:t?ar(this._origin,this._capacity,this._level,this._root,this._tail,t,this.__hash):0===this.size?cr():(this.__ownerID=t,this.__altered=!1,this)},e}(O);nr.isList=rr;var ir=nr.prototype;ir[er]=!0,ir[e]=ir.remove,ir.merge=ir.concat,ir.setIn=le,ir.deleteIn=ir.removeIn=ye,ir.update=ge,ir.updateIn=we,ir.mergeIn=je,ir.mergeDeepIn=qe,ir.withMutations=Me,ir.wasAltered=Ae,ir.asImmutable=xe,ir["@@transducer/init"]=ir.asMutable=De,ir["@@transducer/step"]=function(t,e){return t.push(e)},ir["@@transducer/result"]=function(t){return t.asImmutable()};var or=function(t,e){this.array=t,this.ownerID=e};or.prototype.removeBefore=function(t,e,n){if(0==(n&(1<<e+r)-1)||0===this.array.length)return this;var o=n>>>e&i;if(o>=this.array.length)return new or([],t);var u,s=0===o;if(e>0){var a=this.array[o];if((u=a&&a.removeBefore(t,e-r,n))===a&&s)return this}if(s&&!u)return this;var c=hr(this,t);if(!s)for(var f=0;f<o;f++)c.array[f]=void 0;return u&&(c.array[o]=u),c},or.prototype.removeAfter=function(t,e,o){if(o===(e?1<<e+r:n)||0===this.array.length)return this;var u,s=o-1>>>e&i;if(s>=this.array.length)return this;if(e>0){var a=this.array[s];if((u=a&&a.removeAfter(t,e-r,o))===a&&s===this.array.length-1)return this}var c=hr(this,t);return c.array.splice(s+1),u&&(c.array[s]=u),c};var ur={};function sr(t,e){var i=t._origin,o=t._capacity,u=lr(o),s=t._tail;return a(t._root,t._level,0);function a(t,c,f){return 0===c?function(t,r){var a=r===u?s&&s.array:t&&t.array,c=r>i?0:i-r,f=o-r;f>n&&(f=n);return function(){if(c===f)return ur;var t=e?--f:c++;return a&&a[t]}}(t,f):function(t,u,s){var c,f=t&&t.array,h=s>i?0:i-s>>u,p=1+(o-s>>u);p>n&&(p=n);return function(){for(;;){if(c){var t=c();if(t!==ur)return t;c=null}if(h===p)return ur;var n=e?--p:h++;c=a(f&&f[n],u-r,s+(n<<u))}}}(t,c,f)}}function ar(t,e,r,n,i,o,u){var s=Object.create(ir);return s.size=e-t,s._origin=t,s._capacity=e,s._level=r,s._root=n,s._tail=i,s.__ownerID=o,s.__hash=u,s.__altered=!1,s}function cr(){return ar(0,0,r)}function fr(t,e,n,o,s,a){var c,f=o>>>n&i,h=t&&f<t.array.length;if(!h&&void 0===s)return t;if(n>0){var p=t&&t.array[f],_=fr(p,e,n-r,o,s,a);return _===p?t:((c=hr(t,e)).array[f]=_,c)}return h&&t.array[f]===s?t:(a&&u(a),c=hr(t,e),void 0===s&&f===c.array.length-1?c.array.pop():c.array[f]=s,c)}function hr(t,e){return e&&t&&e===t.ownerID?t:new or(t?t.array.slice():[],e)}function pr(t,e){if(e>=lr(t._capacity))return t._tail;if(e<1<<t._level+r){for(var n=t._root,o=t._level;n&&o>0;)n=n.array[e>>>o&i],o-=r;return n}}function _r(t,e,n){void 0!==e&&(e|=0),void 0!==n&&(n|=0);var o=t.__ownerID||new s,u=t._origin,a=t._capacity,c=u+e,f=void 0===n?a:n<0?a+n:u+n;if(c===u&&f===a)return t;if(c>=f)return t.clear();for(var h=t._level,p=t._root,_=0;c+_<0;)p=new or(p&&p.array.length?[void 0,p]:[],o),_+=1<<(h+=r);_&&(c+=_,u+=_,f+=_,a+=_);for(var l=lr(a),v=lr(f);v>=1<<h+r;)p=new or(p&&p.array.length?[p]:[],o),h+=r;var y=t._tail,d=v<l?pr(t,f-1):v>l?new or([],o):y;if(y&&v>l&&c<a&&y.array.length){for(var g=p=hr(p,o),w=h;w>r;w-=r){var m=l>>>w&i;g=g.array[m]=hr(g.array[m],o)}g.array[l>>>r&i]=y}if(f<a&&(d=d&&d.removeAfter(o,0,f)),c>=v)c-=v,f-=v,h=r,p=null,d=d&&d.removeBefore(o,0,c);else if(c>u||v<l){for(_=0;p;){var b=c>>>h&i;if(b!==v>>>h&i)break;b&&(_+=(1<<h)*b),h-=r,p=p.array[b]}p&&c>u&&(p=p.removeBefore(o,h,c-_)),p&&v<l&&(p=p.removeAfter(o,h,v-_)),_&&(c-=_,f-=_)}return t.__ownerID?(t.size=f-c,t._origin=c,t._capacity=f,t._level=h,t._root=p,t._tail=d,t.__hash=void 0,t.__altered=!0,t):ar(c,f,h,p,d)}function lr(t){return t<n?0:t-1>>>r<<r}var vr,yr=function(t){function e(t){return null==t?gr():ct(t)?t:gr().withMutations((function(e){var r=I(t);te(r.size),r.forEach((function(t,r){return e.set(r,t)}))}))}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.of=function(){return this(arguments)},e.prototype.toString=function(){return this.__toString("OrderedMap {","}")},e.prototype.get=function(t,e){var r=this._map.get(t);return void 0!==r?this._list.get(r)[1]:e},e.prototype.clear=function(){return 0===this.size?this:this.__ownerID?(this.size=0,this._map.clear(),this._list.clear(),this.__altered=!0,this):gr()},e.prototype.set=function(t,e){return wr(this,t,e)},e.prototype.remove=function(t){return wr(this,t,o)},e.prototype.__iterate=function(t,e){var r=this;return this._list.__iterate((function(e){return e&&t(e[1],e[0],r)}),e)},e.prototype.__iterator=function(t,e){return this._list.fromEntrySeq().__iterator(t,e)},e.prototype.__ensureOwner=function(t){if(t===this.__ownerID)return this;var e=this._map.__ensureOwner(t),r=this._list.__ensureOwner(t);return t?dr(e,r,t,this.__hash):0===this.size?gr():(this.__ownerID=t,this.__altered=!1,this._map=e,this._list=r,this)},e}(ke);function dr(t,e,r,n){var i=Object.create(yr.prototype);return i.size=t?t.size:0,i._map=t,i._list=e,i.__ownerID=r,i.__hash=n,i.__altered=!1,i}function gr(){return vr||(vr=dr(Je(),cr()))}function wr(t,e,r){var i,u,s=t._map,a=t._list,c=s.get(e),f=void 0!==c;if(r===o){if(!f)return t;a.size>=n&&a.size>=2*s.size?(i=(u=a.filter((function(t,e){return void 0!==t&&c!==e}))).toKeyedSeq().map((function(t){return t[0]})).flip().toMap(),t.__ownerID&&(i.__ownerID=u.__ownerID=t.__ownerID)):(i=s.remove(e),u=c===a.size-1?a.pop():a.set(c,void 0))}else if(f){if(r===a.get(c)[1])return t;i=s,u=a.set(c,[e,r])}else i=s.set(e,a.size),u=a.set(a.size,[e,r]);return t.__ownerID?(t.size=i.size,t._map=i,t._list=u,t.__hash=void 0,t.__altered=!0,t):dr(i,u)}yr.isOrderedMap=ct,yr.prototype[A]=!0,yr.prototype[e]=yr.prototype.remove;var mr="@@__IMMUTABLE_STACK__@@";function br(t){return Boolean(t&&t[mr])}var zr=function(t){function e(t){return null==t?Er():br(t)?t:Er().pushAll(t)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.of=function(){return this(arguments)},e.prototype.toString=function(){return this.__toString("Stack [","]")},e.prototype.get=function(t,e){var r=this._head;for(t=c(this,t);r&&t--;)r=r.next;return r?r.value:e},e.prototype.peek=function(){return this._head&&this._head.value},e.prototype.push=function(){var t=arguments;if(0===arguments.length)return this;for(var e=this.size+arguments.length,r=this._head,n=arguments.length-1;n>=0;n--)r={value:t[n],next:r};return this.__ownerID?(this.size=e,this._head=r,this.__hash=void 0,this.__altered=!0,this):Or(e,r)},e.prototype.pushAll=function(e){if(0===(e=t(e)).size)return this;if(0===this.size&&br(e))return e;te(e.size);var r=this.size,n=this._head;return e.__iterate((function(t){r++,n={value:t,next:n}}),!0),this.__ownerID?(this.size=r,this._head=n,this.__hash=void 0,this.__altered=!0,this):Or(r,n)},e.prototype.pop=function(){return this.slice(1)},e.prototype.clear=function(){return 0===this.size?this:this.__ownerID?(this.size=0,this._head=void 0,this.__hash=void 0,this.__altered=!0,this):Er()},e.prototype.slice=function(e,r){if(h(e,r,this.size))return this;var n=p(e,this.size);if(_(r,this.size)!==this.size)return t.prototype.slice.call(this,e,r);for(var i=this.size-n,o=this._head;n--;)o=o.next;return this.__ownerID?(this.size=i,this._head=o,this.__hash=void 0,this.__altered=!0,this):Or(i,o)},e.prototype.__ensureOwner=function(t){return t===this.__ownerID?this:t?Or(this.size,this._head,t,this.__hash):0===this.size?Er():(this.__ownerID=t,this.__altered=!1,this)},e.prototype.__iterate=function(t,e){var r=this;if(e)return new $(this.toArray()).__iterate((function(e,n){return t(e,n,r)}),e);for(var n=0,i=this._head;i&&!1!==t(i.value,n++,this);)i=i.next;return n},e.prototype.__iterator=function(t,e){if(e)return new $(this.toArray()).__iterator(t,e);var r=0,n=this._head;return new C((function(){if(n){var e=n.value;return n=n.next,P(t,r++,e)}return{value:void 0,done:!0}}))},e}(O);zr.isStack=br;var Sr,Ir=zr.prototype;function Or(t,e,r,n){var i=Object.create(Ir);return i.size=t,i._head=e,i.__ownerID=r,i.__hash=n,i.__altered=!1,i}function Er(){return Sr||(Sr=Or(0))}Ir[mr]=!0,Ir.shift=Ir.pop,Ir.unshift=Ir.push,Ir.unshiftAll=Ir.pushAll,Ir.withMutations=Me,Ir.wasAltered=Ae,Ir.asImmutable=xe,Ir["@@transducer/init"]=Ir.asMutable=De,Ir["@@transducer/step"]=function(t,e){return t.unshift(e)},Ir["@@transducer/result"]=function(t){return t.asImmutable()};var jr="@@__IMMUTABLE_SET__@@";function qr(t){return Boolean(t&&t[jr])}function Mr(t){return qr(t)&&k(t)}function Dr(t,e){if(t===e)return!0;if(!d(e)||void 0!==t.size&&void 0!==e.size&&t.size!==e.size||void 0!==t.__hash&&void 0!==e.__hash&&t.__hash!==e.__hash||w(t)!==w(e)||b(t)!==b(e)||k(t)!==k(e))return!1;if(0===t.size&&0===e.size)return!0;var r=!z(t);if(k(t)){var n=t.entries();return e.every((function(t,e){var i=n.next().value;return i&&ht(i[1],t)&&(r||ht(i[0],e))}))&&n.next().done}var i=!1;if(void 0===t.size)if(void 0===e.size)"function"==typeof t.cacheResult&&t.cacheResult();else{i=!0;var u=t;t=e,e=u}var s=!0,a=e.__iterate((function(e,n){if(r?!t.has(e):i?!ht(e,t.get(n,o)):!ht(t.get(n,o),e))return s=!1,!1}));return s&&t.size===a}function xr(t,e){var r=function(r){t.prototype[r]=e[r]};return Object.keys(e).forEach(r),Object.getOwnPropertySymbols&&Object.getOwnPropertySymbols(e).forEach(r),t}function Ar(t){if(!t||"object"!=typeof t)return t;if(!d(t)){if(!ie(t))return t;t=X(t)}if(w(t)){var e={};return t.__iterate((function(t,r){e[r]=Ar(t)})),e}var r=[];return t.__iterate((function(t){r.push(Ar(t))})),r}var kr=function(t){function e(e){return null==e?Kr():qr(e)&&!k(e)?e:Kr().withMutations((function(r){var n=t(e);te(n.size),n.forEach((function(t){return r.add(t)}))}))}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.of=function(){return this(arguments)},e.fromKeys=function(t){return this(I(t).keySeq())},e.intersect=function(t){return(t=S(t).toArray()).length?Ur.intersect.apply(e(t.pop()),t):Kr()},e.union=function(t){return(t=S(t).toArray()).length?Ur.union.apply(e(t.pop()),t):Kr()},e.prototype.toString=function(){return this.__toString("Set {","}")},e.prototype.has=function(t){return this._map.has(t)},e.prototype.add=function(t){return Tr(this,this._map.set(t,t))},e.prototype.remove=function(t){return Tr(this,this._map.remove(t))},e.prototype.clear=function(){return Tr(this,this._map.clear())},e.prototype.map=function(t,e){var r=this,n=!1,i=Tr(this,this._map.mapEntries((function(i){var o=i[1],u=t.call(e,o,o,r);return u!==o&&(n=!0),[u,u]}),e));return n?i:this},e.prototype.union=function(){for(var e=[],r=arguments.length;r--;)e[r]=arguments[r];return 0===(e=e.filter((function(t){return 0!==t.size}))).length?this:0!==this.size||this.__ownerID||1!==e.length?this.withMutations((function(r){for(var n=0;n<e.length;n++)"string"==typeof e[n]?r.add(e[n]):t(e[n]).forEach((function(t){return r.add(t)}))})):this.constructor(e[0])},e.prototype.intersect=function(){for(var e=[],r=arguments.length;r--;)e[r]=arguments[r];if(0===e.length)return this;e=e.map((function(e){return t(e)}));var n=[];return this.forEach((function(t){e.every((function(e){return e.includes(t)}))||n.push(t)})),this.withMutations((function(t){n.forEach((function(e){t.remove(e)}))}))},e.prototype.subtract=function(){for(var e=[],r=arguments.length;r--;)e[r]=arguments[r];if(0===e.length)return this;e=e.map((function(e){return t(e)}));var n=[];return this.forEach((function(t){e.some((function(e){return e.includes(t)}))&&n.push(t)})),this.withMutations((function(t){n.forEach((function(e){t.remove(e)}))}))},e.prototype.sort=function(t){return nn(Wt(this,t))},e.prototype.sortBy=function(t,e){return nn(Wt(this,e,t))},e.prototype.wasAltered=function(){return this._map.wasAltered()},e.prototype.__iterate=function(t,e){var r=this;return this._map.__iterate((function(e){return t(e,e,r)}),e)},e.prototype.__iterator=function(t,e){return this._map.__iterator(t,e)},e.prototype.__ensureOwner=function(t){if(t===this.__ownerID)return this;var e=this._map.__ensureOwner(t);return t?this.__make(e,t):0===this.size?this.__empty():(this.__ownerID=t,this._map=e,this)},e}(E);kr.isSet=qr;var Rr,Ur=kr.prototype;function Tr(t,e){return t.__ownerID?(t.size=e.size,t._map=e,t):e===t._map?t:0===e.size?t.__empty():t.__make(e)}function Br(t,e){var r=Object.create(Ur);return r.size=t?t.size:0,r._map=t,r.__ownerID=e,r}function Kr(){return Rr||(Rr=Br(Je()))}Ur[jr]=!0,Ur[e]=Ur.remove,Ur.merge=Ur.concat=Ur.union,Ur.withMutations=Me,Ur.asImmutable=xe,Ur["@@transducer/init"]=Ur.asMutable=De,Ur["@@transducer/step"]=function(t,e){return t.add(e)},Ur["@@transducer/result"]=function(t){return t.asImmutable()},Ur.__empty=Kr,Ur.__make=Br;var Lr,Cr=function(t){function e(t,r,n){if(void 0===n&&(n=1),!(this instanceof e))return new e(t,r,n);if($t(0!==n,"Cannot step a Range by 0"),$t(void 0!==t,"You must define a start value when using Range"),$t(void 0!==r,"You must define an end value when using Range"),n=Math.abs(n),r<t&&(n=-n),this._start=t,this._end=r,this._step=n,this.size=Math.max(0,Math.ceil((r-t)/n-1)+1),0===this.size){if(Lr)return Lr;Lr=this}}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.toString=function(){return 0===this.size?"Range []":"Range [ "+this._start+"..."+this._end+(1!==this._step?" by "+this._step:"")+" ]"},e.prototype.get=function(t,e){return this.has(t)?this._start+c(this,t)*this._step:e},e.prototype.includes=function(t){var e=(t-this._start)/this._step;return e>=0&&e<this.size&&e===Math.floor(e)},e.prototype.slice=function(t,r){return h(t,r,this.size)?this:(t=p(t,this.size),(r=_(r,this.size))<=t?new e(0,0):new e(this.get(t,this._end),this.get(r,this._end),this._step))},e.prototype.indexOf=function(t){var e=t-this._start;if(e%this._step==0){var r=e/this._step;if(r>=0&&r<this.size)return r}return-1},e.prototype.lastIndexOf=function(t){return this.indexOf(t)},e.prototype.__iterate=function(t,e){for(var r=this.size,n=this._step,i=e?this._start+(r-1)*n:this._start,o=0;o!==r&&!1!==t(i,e?r-++o:o++,this);)i+=e?-n:n;return o},e.prototype.__iterator=function(t,e){var r=this.size,n=this._step,i=e?this._start+(r-1)*n:this._start,o=0;return new C((function(){if(o===r)return{value:void 0,done:!0};var u=i;return i+=e?-n:n,P(t,e?r-++o:o++,u)}))},e.prototype.equals=function(t){return t instanceof e?this._start===t._start&&this._end===t._end&&this._step===t._step:Dr(this,t)},e}(G);function Pr(t,e,r){for(var n=ee(e),i=0;i!==n.length;)if((t=se(t,n[i++],o))===o)return r;return t}function Wr(t,e){return Pr(this,t,e)}function Nr(t,e){return Pr(t,e,o)!==o}function Hr(){te(this.size);var t={};return this.__iterate((function(e,r){t[r]=e})),t}S.Iterator=C,xr(S,{toArray:function(){te(this.size);var t=new Array(this.size||0),e=w(this),r=0;return this.__iterate((function(n,i){t[r++]=e?[i,n]:n})),t},toIndexedSeq:function(){return new xt(this)},toJS:function(){return Ar(this)},toKeyedSeq:function(){return new Dt(this,!0)},toMap:function(){return ke(this.toKeyedSeq())},toObject:Hr,toOrderedMap:function(){return yr(this.toKeyedSeq())},toOrderedSet:function(){return nn(w(this)?this.valueSeq():this)},toSet:function(){return kr(w(this)?this.valueSeq():this)},toSetSeq:function(){return new At(this)},toSeq:function(){return b(this)?this.toIndexedSeq():w(this)?this.toKeyedSeq():this.toSetSeq()},toStack:function(){return zr(w(this)?this.valueSeq():this)},toList:function(){return nr(w(this)?this.valueSeq():this)},toString:function(){return"[Collection]"},__toString:function(t,e){return 0===this.size?t+e:t+" "+this.toSeq().map(this.__toStringMapper).join(", ")+" "+e},concat:function(){for(var t=[],e=arguments.length;e--;)t[e]=arguments[e];return Vt(this,function(t,e){var r=w(t),n=[t].concat(e).map((function(t){return d(t)?r&&(t=I(t)):t=r?it(t):ot(Array.isArray(t)?t:[t]),t})).filter((function(t){return 0!==t.size}));if(0===n.length)return t;if(1===n.length){var i=n[0];if(i===t||r&&w(i)||b(t)&&b(i))return i}return new Ct(n)}(this,t))},includes:function(t){return this.some((function(e){return ht(e,t)}))},entries:function(){return this.__iterator(T)},every:function(t,e){te(this.size);var r=!0;return this.__iterate((function(n,i,o){if(!t.call(e,n,i,o))return r=!1,!1})),r},filter:function(t,e){return Vt(this,Bt(this,t,e,!0))},partition:function(t,e){return function(t,e,r){var n=w(t),i=[[],[]];t.__iterate((function(o,u){i[e.call(r,o,u,t)?1:0].push(n?[u,o]:o)}));var o=Qt(t);return i.map((function(e){return Vt(t,o(e))}))}(this,t,e)},find:function(t,e,r){var n=this.findEntry(t,e);return n?n[1]:r},forEach:function(t,e){return te(this.size),this.__iterate(e?t.bind(e):t)},join:function(t){te(this.size),t=void 0!==t?""+t:",";var e="",r=!0;return this.__iterate((function(n){r?r=!1:e+=t,e+=null!=n?n.toString():""})),e},keys:function(){return this.__iterator(R)},map:function(t,e){return Vt(this,Ut(this,t,e))},reduce:function(t,e,r){return Xr(this,t,e,r,arguments.length<2,!1)},reduceRight:function(t,e,r){return Xr(this,t,e,r,arguments.length<2,!0)},reverse:function(){return Vt(this,Tt(this,!0))},slice:function(t,e){return Vt(this,Kt(this,t,e,!0))},some:function(t,e){te(this.size);var r=!1;return this.__iterate((function(n,i,o){if(t.call(e,n,i,o))return r=!0,!1})),r},sort:function(t){return Vt(this,Wt(this,t))},values:function(){return this.__iterator(U)},butLast:function(){return this.slice(0,-1)},isEmpty:function(){return void 0!==this.size?0===this.size:!this.some((function(){return!0}))},count:function(t,e){return a(t?this.toSeq().filter(t,e):this)},countBy:function(t,e){return function(t,e,r){var n=ke().asMutable();return t.__iterate((function(i,o){n.update(e.call(r,i,o,t),0,(function(t){return t+1}))})),n.asImmutable()}(this,t,e)},equals:function(t){return Dr(this,t)},entrySeq:function(){var t=this;if(t._cache)return new $(t._cache);var e=t.toSeq().map(Gr).toIndexedSeq();return e.fromEntrySeq=function(){return t.toSeq()},e},filterNot:function(t,e){return this.filter(Zr(t),e)},findEntry:function(t,e,r){var n=r;return this.__iterate((function(r,i,o){if(t.call(e,r,i,o))return n=[i,r],!1})),n},findKey:function(t,e){var r=this.findEntry(t,e);return r&&r[0]},findLast:function(t,e,r){return this.toKeyedSeq().reverse().find(t,e,r)},findLastEntry:function(t,e,r){return this.toKeyedSeq().reverse().findEntry(t,e,r)},findLastKey:function(t,e){return this.toKeyedSeq().reverse().findKey(t,e)},first:function(t){return this.find(f,null,t)},flatMap:function(t,e){return Vt(this,function(t,e,r){var n=Qt(t);return t.toSeq().map((function(i,o){return n(e.call(r,i,o,t))})).flatten(!0)}(this,t,e))},flatten:function(t){return Vt(this,Pt(this,t,!0))},fromEntrySeq:function(){return new kt(this)},get:function(t,e){return this.find((function(e,r){return ht(r,t)}),void 0,e)},getIn:Wr,groupBy:function(t,e){return function(t,e,r){var n=w(t),i=(k(t)?yr():ke()).asMutable();t.__iterate((function(o,u){i.update(e.call(r,o,u,t),(function(t){return(t=t||[]).push(n?[u,o]:o),t}))}));var o=Qt(t);return i.map((function(e){return Vt(t,o(e))})).asImmutable()}(this,t,e)},has:function(t){return this.get(t,o)!==o},hasIn:function(t){return Nr(this,t)},isSubset:function(t){return t="function"==typeof t.includes?t:S(t),this.every((function(e){return t.includes(e)}))},isSuperset:function(t){return(t="function"==typeof t.isSubset?t:S(t)).isSubset(this)},keyOf:function(t){return this.findKey((function(e){return ht(e,t)}))},keySeq:function(){return this.toSeq().map(Fr).toIndexedSeq()},last:function(t){return this.toSeq().reverse().first(t)},lastKeyOf:function(t){return this.toKeyedSeq().reverse().keyOf(t)},max:function(t){return Nt(this,t)},maxBy:function(t,e){return Nt(this,e,t)},min:function(t){return Nt(this,t?$r(t):en)},minBy:function(t,e){return Nt(this,e?$r(e):en,t)},rest:function(){return this.slice(1)},skip:function(t){return 0===t?this:this.slice(Math.max(0,t))},skipLast:function(t){return 0===t?this:this.slice(0,-Math.max(0,t))},skipWhile:function(t,e){return Vt(this,Lt(this,t,e,!0))},skipUntil:function(t,e){return this.skipWhile(Zr(t),e)},sortBy:function(t,e){return Vt(this,Wt(this,e,t))},take:function(t){return this.slice(0,Math.max(0,t))},takeLast:function(t){return this.slice(-Math.max(0,t))},takeWhile:function(t,e){return Vt(this,function(t,e,r){var n=Xt(t);return n.__iterateUncached=function(n,i){var o=this;if(i)return this.cacheResult().__iterate(n,i);var u=0;return t.__iterate((function(t,i,s){return e.call(r,t,i,s)&&++u&&n(t,i,o)})),u},n.__iteratorUncached=function(n,i){var o=this;if(i)return this.cacheResult().__iterator(n,i);var u=t.__iterator(T,i),s=!0;return new C((function(){if(!s)return{value:void 0,done:!0};var t=u.next();if(t.done)return t;var i=t.value,a=i[0],c=i[1];return e.call(r,c,a,o)?n===T?t:P(n,a,c,t):(s=!1,{value:void 0,done:!0})}))},n}(this,t,e))},takeUntil:function(t,e){return this.takeWhile(Zr(t),e)},update:function(t){return t(this)},valueSeq:function(){return this.toIndexedSeq()},hashCode:function(){return this.__hash||(this.__hash=function(t){if(t.size===1/0)return 0;var e=k(t),r=w(t),n=e?1:0;return t.__iterate(r?e?function(t,e){n=31*n+rn(vt(t),vt(e))|0}:function(t,e){n=n+rn(vt(t),vt(e))|0}:e?function(t){n=31*n+vt(t)|0}:function(t){n=n+vt(t)|0}),function(t,e){return e=pt(e,3432918353),e=pt(e<<15|e>>>-15,461845907),e=pt(e<<13|e>>>-13,5),e=pt((e=(e+3864292196|0)^t)^e>>>16,2246822507),e=_t((e=pt(e^e>>>13,3266489909))^e>>>16)}(t.size,n)}(this))}});var Jr=S.prototype;Jr[y]=!0,Jr[L]=Jr.values,Jr.toJSON=Jr.toArray,Jr.__toStringMapper=oe,Jr.inspect=Jr.toSource=function(){return this.toString()},Jr.chain=Jr.flatMap,Jr.contains=Jr.includes,xr(I,{flip:function(){return Vt(this,Rt(this))},mapEntries:function(t,e){var r=this,n=0;return Vt(this,this.toSeq().map((function(i,o){return t.call(e,[o,i],n++,r)})).fromEntrySeq())},mapKeys:function(t,e){var r=this;return Vt(this,this.toSeq().flip().map((function(n,i){return t.call(e,n,i,r)})).flip())}});var Vr=I.prototype;Vr[g]=!0,Vr[L]=Jr.entries,Vr.toJSON=Hr,Vr.__toStringMapper=function(t,e){return oe(e)+": "+oe(t)},xr(O,{toKeyedSeq:function(){return new Dt(this,!1)},filter:function(t,e){return Vt(this,Bt(this,t,e,!1))},findIndex:function(t,e){var r=this.findEntry(t,e);return r?r[0]:-1},indexOf:function(t){var e=this.keyOf(t);return void 0===e?-1:e},lastIndexOf:function(t){var e=this.lastKeyOf(t);return void 0===e?-1:e},reverse:function(){return Vt(this,Tt(this,!1))},slice:function(t,e){return Vt(this,Kt(this,t,e,!1))},splice:function(t,e){var r=arguments.length;if(e=Math.max(e||0,0),0===r||2===r&&!e)return this;t=p(t,t<0?this.count():this.size);var n=this.slice(0,t);return Vt(this,1===r?n:n.concat(Zt(arguments,2),this.slice(t+e)))},findLastIndex:function(t,e){var r=this.findLastEntry(t,e);return r?r[0]:-1},first:function(t){return this.get(0,t)},flatten:function(t){return Vt(this,Pt(this,t,!1))},get:function(t,e){return(t=c(this,t))<0||this.size===1/0||void 0!==this.size&&t>this.size?e:this.find((function(e,r){return r===t}),void 0,e)},has:function(t){return(t=c(this,t))>=0&&(void 0!==this.size?this.size===1/0||t<this.size:-1!==this.indexOf(t))},interpose:function(t){return Vt(this,function(t,e){var r=Xt(t);return r.size=t.size&&2*t.size-1,r.__iterateUncached=function(r,n){var i=this,o=0;return t.__iterate((function(t){return(!o||!1!==r(e,o++,i))&&!1!==r(t,o++,i)}),n),o},r.__iteratorUncached=function(r,n){var i,o=t.__iterator(U,n),u=0;return new C((function(){return(!i||u%2)&&(i=o.next()).done?i:u%2?P(r,u++,e):P(r,u++,i.value,i)}))},r}(this,t))},interleave:function(){var t=[this].concat(Zt(arguments)),e=Jt(this.toSeq(),G.of,t),r=e.flatten(!0);return e.size&&(r.size=e.size*t.length),Vt(this,r)},keySeq:function(){return Cr(0,this.size)},last:function(t){return this.get(-1,t)},skipWhile:function(t,e){return Vt(this,Lt(this,t,e,!1))},zip:function(){return Vt(this,Jt(this,tn,[this].concat(Zt(arguments))))},zipAll:function(){return Vt(this,Jt(this,tn,[this].concat(Zt(arguments)),!0))},zipWith:function(t){var e=Zt(arguments);return e[0]=this,Vt(this,Jt(this,t,e))}});var Yr=O.prototype;Yr[m]=!0,Yr[A]=!0,xr(E,{get:function(t,e){return this.has(t)?t:e},includes:function(t){return this.has(t)},keySeq:function(){return this.valueSeq()}});var Qr=E.prototype;function Xr(t,e,r,n,i,o){return te(t.size),t.__iterate((function(t,o,u){i?(i=!1,r=t):r=e.call(n,r,t,o,u)}),o),r}function Fr(t,e){return e}function Gr(t,e){return[e,t]}function Zr(t){return function(){return!t.apply(this,arguments)}}function $r(t){return function(){return-t.apply(this,arguments)}}function tn(){return Zt(arguments)}function en(t,e){return t<e?1:t>e?-1:0}function rn(t,e){return t^e+2654435769+(t<<6)+(t>>2)|0}Qr.has=Jr.includes,Qr.contains=Qr.includes,Qr.keys=Qr.values,xr(F,Vr),xr(G,Yr),xr(Z,Qr);var nn=function(t){function e(t){return null==t?an():Mr(t)?t:an().withMutations((function(e){var r=E(t);te(r.size),r.forEach((function(t){return e.add(t)}))}))}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.of=function(){return this(arguments)},e.fromKeys=function(t){return this(I(t).keySeq())},e.prototype.toString=function(){return this.__toString("OrderedSet {","}")},e}(kr);nn.isOrderedSet=Mr;var on,un=nn.prototype;function sn(t,e){var r=Object.create(un);return r.size=t?t.size:0,r._map=t,r.__ownerID=e,r}function an(){return on||(on=sn(gr()))}un[A]=!0,un.zip=Yr.zip,un.zipWith=Yr.zipWith,un.zipAll=Yr.zipAll,un.__empty=an,un.__make=sn;var cn=function(t,e){var r;!function(t){if(D(t))throw new Error("Can not call `Record` with an immutable Record as default values. Use a plain javascript object instead.");if(x(t))throw new Error("Can not call `Record` with an immutable Collection as default values. Use a plain javascript object instead.");if(null===t||"object"!=typeof t)throw new Error("Can not call `Record` with a non-object as default values. Use a plain javascript object instead.")}(t);var n=function(o){var u=this;if(o instanceof n)return o;if(!(this instanceof n))return new n(o);if(!r){r=!0;var s=Object.keys(t),a=i._indices={};i._name=e,i._keys=s,i._defaultValues=t;for(var c=0;c<s.length;c++){var f=s[c];a[f]=c,i[f]?"object"==typeof console&&console.warn&&console.warn("Cannot define "+pn(this)+' with property "'+f+'" since that property name is part of the Record API.'):ln(i,f)}}return this.__ownerID=void 0,this._values=nr().withMutations((function(t){t.setSize(u._keys.length),I(o).forEach((function(e,r){t.set(u._indices[r],e===u._defaultValues[r]?void 0:e)}))})),this},i=n.prototype=Object.create(fn);return i.constructor=n,e&&(n.displayName=e),n};cn.prototype.toString=function(){for(var t,e=pn(this)+" { ",r=this._keys,n=0,i=r.length;n!==i;n++)e+=(n?", ":"")+(t=r[n])+": "+oe(this.get(t));return e+" }"},cn.prototype.equals=function(t){return this===t||D(t)&&_n(this).equals(_n(t))},cn.prototype.hashCode=function(){return _n(this).hashCode()},cn.prototype.has=function(t){return this._indices.hasOwnProperty(t)},cn.prototype.get=function(t,e){if(!this.has(t))return e;var r=this._indices[t],n=this._values.get(r);return void 0===n?this._defaultValues[t]:n},cn.prototype.set=function(t,e){if(this.has(t)){var r=this._values.set(this._indices[t],e===this._defaultValues[t]?void 0:e);if(r!==this._values&&!this.__ownerID)return hn(this,r)}return this},cn.prototype.remove=function(t){return this.set(t)},cn.prototype.clear=function(){var t=this._values.clear().setSize(this._keys.length);return this.__ownerID?this:hn(this,t)},cn.prototype.wasAltered=function(){return this._values.wasAltered()},cn.prototype.toSeq=function(){return _n(this)},cn.prototype.toJS=function(){return Ar(this)},cn.prototype.entries=function(){return this.__iterator(T)},cn.prototype.__iterator=function(t,e){return _n(this).__iterator(t,e)},cn.prototype.__iterate=function(t,e){return _n(this).__iterate(t,e)},cn.prototype.__ensureOwner=function(t){if(t===this.__ownerID)return this;var e=this._values.__ensureOwner(t);return t?hn(this,e,t):(this.__ownerID=t,this._values=e,this)},cn.isRecord=D,cn.getDescriptiveName=pn;var fn=cn.prototype;function hn(t,e,r){var n=Object.create(Object.getPrototypeOf(t));return n._values=e,n.__ownerID=r,n}function pn(t){return t.constructor.displayName||t.constructor.name||"Record"}function _n(t){return it(t._keys.map((function(e){return[e,t.get(e)]})))}function ln(t,e){try{Object.defineProperty(t,e,{get:function(){return this.get(e)},set:function(t){$t(this.__ownerID,"Cannot set on an immutable record."),this.set(e,t)}})}catch(t){}}fn[M]=!0,fn[e]=fn.remove,fn.deleteIn=fn.removeIn=ye,fn.getIn=Wr,fn.hasIn=Jr.hasIn,fn.merge=me,fn.mergeWith=be,fn.mergeIn=je,fn.mergeDeep=Oe,fn.mergeDeepWith=Ee,fn.mergeDeepIn=qe,fn.setIn=le,fn.update=ge,fn.updateIn=we,fn.withMutations=Me,fn.asMutable=De,fn.asImmutable=xe,fn[L]=fn.entries,fn.toJSON=fn.toObject=Jr.toObject,fn.inspect=fn.toSource=function(){return this.toString()};var vn,yn=function(t){function e(t,r){if(!(this instanceof e))return new e(t,r);if(this._value=t,this.size=void 0===r?1/0:Math.max(0,r),0===this.size){if(vn)return vn;vn=this}}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.toString=function(){return 0===this.size?"Repeat []":"Repeat [ "+this._value+" "+this.size+" times ]"},e.prototype.get=function(t,e){return this.has(t)?this._value:e},e.prototype.includes=function(t){return ht(this._value,t)},e.prototype.slice=function(t,r){var n=this.size;return h(t,r,n)?this:new e(this._value,_(r,n)-p(t,n))},e.prototype.reverse=function(){return this},e.prototype.indexOf=function(t){return ht(this._value,t)?0:-1},e.prototype.lastIndexOf=function(t){return ht(this._value,t)?this.size:-1},e.prototype.__iterate=function(t,e){for(var r=this.size,n=0;n!==r&&!1!==t(this._value,e?r-++n:n++,this););return n},e.prototype.__iterator=function(t,e){var r=this,n=this.size,i=0;return new C((function(){return i===n?{value:void 0,done:!0}:P(t,e?n-++i:i++,r._value)}))},e.prototype.equals=function(t){return t instanceof e?ht(this._value,t._value):Dr(this,t)},e}(G);function dn(t,e,r,n,i,o){if("string"!=typeof r&&!x(r)&&(Q(r)||N(r)||ne(r))){if(~t.indexOf(r))throw new TypeError("Cannot convert circular structure to Immutable");t.push(r),i&&""!==n&&i.push(n);var u=e.call(o,n,X(r).map((function(n,o){return dn(t,e,n,o,i,r)})),i&&i.slice());return t.pop(),i&&i.pop(),u}return r}function gn(t,e){return b(e)?e.toList():w(e)?e.toMap():e.toSet()}var wn=S;t.Collection=S,t.Iterable=wn,t.List=nr,t.Map=ke,t.OrderedMap=yr,t.OrderedSet=nn,t.PairSorting={LeftThenRight:-1,RightThenLeft:1},t.Range=Cr,t.Record=cn,t.Repeat=yn,t.Seq=X,t.Set=kr,t.Stack=zr,t.fromJS=function(t,e){return dn([],e||gn,t,"",e&&e.length>2?[]:void 0,{"":t})},t.get=se,t.getIn=Pr,t.has=ue,t.hasIn=Nr,t.hash=vt,t.is=ht,t.isAssociative=z,t.isCollection=d,t.isImmutable=x,t.isIndexed=b,t.isKeyed=w,t.isList=rr,t.isMap=at,t.isOrdered=k,t.isOrderedMap=ct,t.isOrderedSet=Mr,t.isPlainObject=ne,t.isRecord=D,t.isSeq=q,t.isSet=qr,t.isStack=br,t.isValueObject=ft,t.merge=function(t){for(var e=[],r=arguments.length-1;r-- >0;)e[r]=arguments[r+1];return Ie(t,e)},t.mergeDeep=function(t){for(var e=[],r=arguments.length-1;r-- >0;)e[r]=arguments[r+1];return Se(t,e)},t.mergeDeepWith=function(t,e){for(var r=[],n=arguments.length-2;n-- >0;)r[n]=arguments[n+2];return Se(e,r,t)},t.mergeWith=function(t,e){for(var r=[],n=arguments.length-2;n-- >0;)r[n]=arguments[n+2];return Ie(e,r,t)},t.remove=ce,t.removeIn=ve,t.set=fe,t.setIn=_e,t.update=de,t.updateIn=he,t.version="5.1.3"}));
