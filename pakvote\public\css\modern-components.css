/* Modern PakVote UI Components */

/* CSS Variables for Consistent Theming */
:root {
    --primary-color: #2d5a27;
    --primary-light: #4a7c59;
    --primary-dark: #1e3d1a;
    --secondary-color: #f8f9fa;
    --accent-color: #28a745;
    --text-primary: #2c3e50;
    --text-secondary: #6c757d;
    --text-light: #ffffff;
    --border-color: #e9ecef;
    --shadow-light: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 8px rgba(0, 0, 0, 0.15);
    --shadow-heavy: 0 8px 16px rgba(0, 0, 0, 0.2);
    --border-radius: 12px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Global Styles */
body {
    font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
}

.urdu-text {
    font-family: 'Noto Nastaliq Urdu', serif;
    direction: rtl;
}

/* ===== HEADER STYLES ===== */
.main-header {
    position: sticky;
    top: 0;
    z-index: 1030;
    background: var(--text-light);
    box-shadow: var(--shadow-medium);
}

/* Top Bar */
.top-bar {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    color: var(--text-light);
    padding: 8px 0;
    font-size: 0.875rem;
}

.top-info .info-item {
    margin-right: 20px;
    display: inline-flex;
    align-items: center;
    gap: 6px;
}

.top-actions {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 15px;
}

.language-switcher .btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--text-light);
    font-size: 0.8rem;
}

.social-links {
    display: flex;
    gap: 8px;
}

.social-link {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-light);
    text-decoration: none;
    transition: var(--transition);
}

.social-link:hover {
    background: rgba(255, 255, 255, 0.2);
    color: var(--text-light);
    transform: translateY(-2px);
}

/* Main Navbar */
.main-navbar {
    background: var(--text-light);
    padding: 15px 0;
    border-bottom: 1px solid var(--border-color);
}

.brand-container {
    display: flex;
    align-items: center;
}

.logo-wrapper {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-light);
    font-size: 1.5rem;
    box-shadow: var(--shadow-light);
}

.logo-text {
    display: flex;
    flex-direction: column;
}

.brand-name {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    line-height: 1;
}

.brand-tagline {
    font-size: 0.75rem;
    color: var(--text-secondary);
    line-height: 1;
}

.brand-tagline-urdu {
    font-size: 0.7rem;
    margin-top: 2px;
}

/* Navigation Links */
.navbar-nav .nav-link {
    color: var(--text-primary);
    font-weight: 500;
    padding: 8px 16px;
    border-radius: 8px;
    transition: var(--transition);
    margin: 0 4px;
}

.navbar-nav .nav-link:hover {
    background: var(--secondary-color);
    color: var(--primary-color);
}

.navbar-nav .nav-link.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
}

.navbar-nav .nav-link.btn-primary {
    background: var(--primary-color);
    border: 2px solid var(--primary-color);
}

/* User Menu */
.user-menu {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 6px 12px;
    border-radius: 25px;
    background: var(--secondary-color);
    border: 1px solid var(--border-color);
}

.user-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    overflow: hidden;
}

.avatar-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-name {
    font-weight: 500;
    color: var(--text-primary);
}

/* Notifications */
.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #dc3545;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notification-dropdown {
    width: 320px;
    border: none;
    box-shadow: var(--shadow-heavy);
    border-radius: var(--border-radius);
}

.notification-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 0;
}

/* Breadcrumb */
.breadcrumb-section {
    background: var(--secondary-color);
    border-bottom: 1px solid var(--border-color);
    padding: 12px 0;
}

.breadcrumb {
    background: none;
    margin: 0;
    padding: 0;
}

.breadcrumb-item a {
    color: var(--primary-color);
    text-decoration: none;
}

/* ===== SIDEBAR STYLES ===== */
.modern-sidebar {
    position: fixed;
    left: 0;
    top: 0;
    width: 280px;
    height: 100vh;
    background: linear-gradient(180deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: var(--text-light);
    z-index: 1040;
    transition: var(--transition);
    overflow-y: auto;
    box-shadow: var(--shadow-heavy);
}

.modern-sidebar.collapsed {
    width: 70px;
}

/* Sidebar Header */
.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.sidebar-brand {
    display: flex;
    align-items: center;
    gap: 12px;
}

.brand-icon {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.brand-text .brand-name {
    font-size: 1.2rem;
    font-weight: 700;
    line-height: 1;
}

.brand-subtitle {
    font-size: 0.75rem;
    opacity: 0.8;
    line-height: 1;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: var(--text-light);
    font-size: 1.2rem;
    padding: 8px;
    border-radius: 6px;
    transition: var(--transition);
}

.sidebar-toggle:hover {
    background: rgba(255, 255, 255, 0.1);
}

/* Profile Section */
.sidebar-profile {
    padding: 20px;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.profile-avatar {
    position: relative;
    display: inline-block;
    margin-bottom: 12px;
}

.profile-avatar .avatar-img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: 3px solid rgba(255, 255, 255, 0.2);
}

.status-indicator {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 2px solid var(--primary-color);
}

.status-indicator.online {
    background: #28a745;
}

.profile-name {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 4px;
}

.profile-role {
    font-size: 0.8rem;
    opacity: 0.8;
    margin-bottom: 4px;
}

.profile-org {
    font-size: 0.7rem;
    opacity: 0.6;
}

/* Navigation */
.sidebar-nav {
    padding: 20px 0;
}

.nav-section {
    margin-bottom: 30px;
}

.nav-section-title {
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    opacity: 0.6;
    margin-bottom: 12px;
    padding: 0 20px;
}

.nav-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-item {
    margin-bottom: 4px;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: var(--text-light);
    text-decoration: none;
    transition: var(--transition);
    position: relative;
    border-radius: 0;
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-light);
}

.nav-link.active {
    background: rgba(255, 255, 255, 0.15);
    color: var(--text-light);
}

.nav-link.active .nav-indicator {
    opacity: 1;
}

.nav-icon {
    width: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    font-size: 1.1rem;
}

.nav-text {
    flex: 1;
    font-weight: 500;
}

.nav-badge {
    background: rgba(255, 255, 255, 0.2);
    color: var(--text-light);
    font-size: 0.7rem;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 600;
}

.nav-indicator {
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 20px;
    background: var(--text-light);
    border-radius: 0 4px 4px 0;
    opacity: 0;
    transition: var(--transition);
}

/* Sidebar Footer */
.sidebar-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(0, 0, 0, 0.1);
}

.footer-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.8rem;
    opacity: 0.8;
}

.footer-actions {
    display: flex;
    gap: 8px;
}

/* Sidebar Overlay */
.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1035;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.sidebar-overlay.show {
    opacity: 1;
    visibility: visible;
}

/* ===== FOOTER STYLES ===== */
.modern-footer {
    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
    color: var(--text-light);
    margin-top: auto;
    position: relative;
}

.footer-main {
    padding: 60px 0 40px;
}

.footer-section {
    margin-bottom: 30px;
}

.footer-brand .brand-logo {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
}

.footer-brand .brand-logo i {
    font-size: 2rem;
    color: var(--accent-color);
}

.footer-brand .brand-name {
    font-size: 1.5rem;
    font-weight: 700;
}

.brand-description {
    font-size: 0.9rem;
    line-height: 1.6;
    opacity: 0.9;
    margin-bottom: 10px;
}

.brand-description-urdu {
    font-size: 0.85rem;
    margin-bottom: 20px;
}

.footer-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: var(--text-light);
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: 8px;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
}

.footer-links a:hover {
    color: var(--text-light);
    transform: translateX(5px);
}

/* Contact Info */
.contact-info {
    margin-top: 20px;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    margin-bottom: 20px;
}

.contact-icon {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    flex-shrink: 0;
}

.contact-details strong {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
}

.contact-details p {
    margin: 0;
    font-size: 0.9rem;
    opacity: 0.9;
    line-height: 1.5;
}

/* Footer Bottom */
.footer-bottom {
    background: rgba(0, 0, 0, 0.2);
    padding: 20px 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.copyright p {
    margin: 0;
    font-size: 0.85rem;
    opacity: 0.8;
}

.footer-bottom-links {
    display: flex;
    justify-content: flex-end;
    gap: 20px;
}

.footer-bottom-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    font-size: 0.85rem;
    transition: var(--transition);
}

.footer-bottom-links a:hover {
    color: var(--text-light);
}

/* Back to Top */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    color: var(--text-light);
    border: none;
    border-radius: 50%;
    font-size: 1.2rem;
    cursor: pointer;
    transition: var(--transition);
    opacity: 0;
    visibility: hidden;
    z-index: 1000;
    box-shadow: var(--shadow-medium);
}

.back-to-top.show {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    background: var(--primary-dark);
    transform: translateY(-3px);
    box-shadow: var(--shadow-heavy);
}

/* System Status */
.system-status {
    position: fixed;
    bottom: 30px;
    left: 30px;
    background: rgba(255, 255, 255, 0.95);
    padding: 8px 12px;
    border-radius: 20px;
    box-shadow: var(--shadow-light);
    z-index: 1000;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.status-dot.online {
    background: #28a745;
}

.status-text {
    font-size: 0.75rem;
    font-weight: 500;
    color: var(--text-primary);
}

@keyframes pulse {
    0% {
        opacity: 1;
    }

    50% {
        opacity: 0.5;
    }

    100% {
        opacity: 1;
    }
}

/* ===== LAYOUT STYLES ===== */
.app-container {
    display: flex;
    min-height: 100vh;
}

.main-content {
    flex: 1;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
}

.main-content.with-sidebar {
    margin-left: 280px;
    transition: var(--transition);
}

.content-wrapper {
    padding: 30px;
}

.page-content {
    background: transparent;
}

/* Modern Alerts */
.modern-alert {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-medium);
    margin-bottom: 20px;
    overflow: hidden;
}

.alert-content {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 5px;
}

.alert-icon {
    font-size: 1.5rem;
    margin-top: 2px;
    flex-shrink: 0;
}

.alert-text strong {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
}

.alert-text p {
    margin: 0;
    line-height: 1.5;
}

.alert-text ul {
    margin-top: 8px;
    padding-left: 20px;
}

/* Button Enhancements */
.btn {
    border-radius: 8px;
    font-weight: 500;
    padding: 8px 16px;
    transition: var(--transition);
    border: 2px solid transparent;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    border-color: var(--primary-color);
    box-shadow: var(--shadow-light);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.btn-outline-primary {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

/* Form Enhancements */
.form-control {
    border-radius: 8px;
    border: 2px solid var(--border-color);
    padding: 10px 15px;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(45, 90, 39, 0.25);
}

.form-select {
    border-radius: 8px;
    border: 2px solid var(--border-color);
    padding: 10px 15px;
}

.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(45, 90, 39, 0.25);
}

/* Table Enhancements */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-light);
    background: white;
}

.table thead th {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    color: white;
    border: none;
    font-weight: 600;
    padding: 15px;
}

.table tbody td {
    padding: 15px;
    border-color: var(--border-color);
    vertical-align: middle;
}

.table tbody tr:hover {
    background: rgba(45, 90, 39, 0.05);
}

/* Modal Enhancements */
.modal-content {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-heavy);
}

.modal-header {
    border-bottom: 1px solid var(--border-color);
    padding: 20px;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    border-top: 1px solid var(--border-color);
    padding: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .modern-sidebar {
        transform: translateX(-100%);
    }

    .modern-sidebar.show {
        transform: translateX(0);
    }

    .main-content.with-sidebar {
        margin-left: 0;
    }

    .content-wrapper {
        padding: 15px;
    }

    .top-info .info-item {
        display: none;
    }

    .footer-bottom-links {
        justify-content: center;
        margin-top: 15px;
    }

    .contact-item {
        flex-direction: column;
        text-align: center;
    }

    .alert-content {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }
}