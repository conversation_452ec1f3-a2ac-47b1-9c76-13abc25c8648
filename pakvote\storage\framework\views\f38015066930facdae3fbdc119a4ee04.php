<?php $__env->startSection('title', 'Welcome to PakVote'); ?>

<?php $__env->startSection('content'); ?>
<div class="home-container">
    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="row align-items-center min-vh-75">
                <div class="col-lg-6">
                    <div class="hero-content">
                        <h1 class="hero-title">
                            Welcome to <span class="text-primary">PakVote</span>
                            <br>
                            <span class="urdu-text">پاک ووٹ میں خوش آمدید</span>
                        </h1>
                        <p class="hero-subtitle">
                            Pakistan's most advanced election management system, designed to ensure 
                            transparent, secure, and efficient democratic processes.
                        </p>
                        <p class="hero-subtitle-urdu urdu-text">
                            پاکستان کا جدید ترین انتخابی نظام، شفاف اور محفوظ جمہوری عمل کے لیے
                        </p>
                        
                        <div class="hero-actions">
                            <?php if(auth()->guard()->guest()): ?>
                                <a href="<?php echo e(route('register')); ?>" class="btn btn-primary btn-lg me-3">
                                    <i class="bi bi-person-plus"></i> Get Started
                                </a>
                                <a href="<?php echo e(route('login')); ?>" class="btn btn-outline-primary btn-lg">
                                    <i class="bi bi-box-arrow-in-right"></i> Login
                                </a>
                            <?php else: ?>
                                <?php if(auth()->user()->isAdmin()): ?>
                                    <a href="<?php echo e(route('admin.dashboard')); ?>" class="btn btn-primary btn-lg me-3">
                                        <i class="bi bi-speedometer2"></i> Admin Dashboard
                                    </a>
                                <?php elseif(auth()->user()->isCandidate()): ?>
                                    <a href="<?php echo e(route('candidate.dashboard')); ?>" class="btn btn-primary btn-lg me-3">
                                        <i class="bi bi-person-badge"></i> Campaign Dashboard
                                    </a>
                                <?php endif; ?>
                                <a href="<?php echo e(route('admin.modern-demo')); ?>" class="btn btn-outline-primary btn-lg">
                                    <i class="bi bi-palette"></i> View Modern UI
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="hero-image">
                        <div class="hero-card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">
                                    <i class="bi bi-shield-check"></i> Secure & Transparent
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="feature-list">
                                    <div class="feature-item">
                                        <i class="bi bi-check-circle text-success"></i>
                                        <span>ECP Compliant System</span>
                                    </div>
                                    <div class="feature-item">
                                        <i class="bi bi-check-circle text-success"></i>
                                        <span>Multi-language Support</span>
                                    </div>
                                    <div class="feature-item">
                                        <i class="bi bi-check-circle text-success"></i>
                                        <span>Real-time Analytics</span>
                                    </div>
                                    <div class="feature-item">
                                        <i class="bi bi-check-circle text-success"></i>
                                        <span>Mobile Responsive</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features-section py-5">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="section-title">
                    <i class="bi bi-star text-warning"></i> Key Features
                </h2>
                <p class="section-subtitle">
                    Comprehensive tools for modern election management
                </p>
            </div>
            
            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon bg-primary">
                            <i class="bi bi-people"></i>
                        </div>
                        <h4>User Management</h4>
                        <p>Complete user registration, verification, and role-based access control system.</p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon bg-success">
                            <i class="bi bi-building"></i>
                        </div>
                        <h4>Organization Portal</h4>
                        <p>Multi-tenant architecture supporting multiple political organizations.</p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon bg-warning">
                            <i class="bi bi-flag"></i>
                        </div>
                        <h4>Party Management</h4>
                        <p>Register and manage political parties with ECP compliance features.</p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon bg-info">
                            <i class="bi bi-geo-alt"></i>
                        </div>
                        <h4>Constituency Mapping</h4>
                        <p>Complete constituency management with geographical boundaries.</p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon bg-danger">
                            <i class="bi bi-person-badge"></i>
                        </div>
                        <h4>Candidate Portal</h4>
                        <p>Dedicated dashboard for candidates to manage their campaigns.</p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon bg-dark">
                            <i class="bi bi-graph-up"></i>
                        </div>
                        <h4>Analytics & Reports</h4>
                        <p>Comprehensive reporting and analytics for election insights.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Statistics Section -->
    <section class="stats-section py-5 bg-light">
        <div class="container">
            <div class="row text-center">
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stat-item">
                        <div class="stat-number text-primary">1,234</div>
                        <div class="stat-label">Registered Users</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stat-item">
                        <div class="stat-number text-success">89</div>
                        <div class="stat-label">Organizations</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stat-item">
                        <div class="stat-number text-warning">45</div>
                        <div class="stat-label">Political Parties</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stat-item">
                        <div class="stat-number text-info">272</div>
                        <div class="stat-label">Constituencies</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section py-5">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8 text-center">
                    <h2 class="cta-title">Ready to Get Started?</h2>
                    <p class="cta-subtitle">
                        Join thousands of users who trust PakVote for their election management needs.
                    </p>
                    <div class="cta-actions">
                        <?php if(auth()->guard()->guest()): ?>
                            <a href="<?php echo e(route('register')); ?>" class="btn btn-primary btn-lg me-3">
                                <i class="bi bi-rocket"></i> Start Your Journey
                            </a>
                            <a href="#" class="btn btn-outline-primary btn-lg">
                                <i class="bi bi-play-circle"></i> Watch Demo
                            </a>
                        <?php else: ?>
                            <a href="<?php echo e(route('admin.modern-demo')); ?>" class="btn btn-primary btn-lg me-3">
                                <i class="bi bi-palette"></i> Explore Modern UI
                            </a>
                            <a href="<?php echo e(route('admin.dashboard')); ?>" class="btn btn-outline-primary btn-lg">
                                <i class="bi bi-speedometer2"></i> Go to Dashboard
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.home-container {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.hero-section {
    padding: 80px 0;
    background: linear-gradient(135deg, rgba(45, 90, 39, 0.05) 0%, rgba(40, 167, 69, 0.05) 100%);
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 20px;
    color: var(--text-primary);
}

.hero-subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 15px;
    line-height: 1.6;
}

.hero-subtitle-urdu {
    font-size: 1.1rem;
    margin-bottom: 30px;
    opacity: 0.8;
}

.hero-actions {
    margin-top: 30px;
}

.hero-card {
    background: white;
    border-radius: 16px;
    box-shadow: var(--shadow-heavy);
    overflow: hidden;
    transform: rotate(3deg);
    transition: var(--transition);
}

.hero-card:hover {
    transform: rotate(0deg) scale(1.05);
}

.feature-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 500;
}

.features-section {
    background: white;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 15px;
}

.section-subtitle {
    font-size: 1.1rem;
    color: var(--text-secondary);
}

.feature-card {
    text-align: center;
    padding: 40px 20px;
    background: white;
    border-radius: 16px;
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    height: 100%;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-heavy);
}

.feature-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 2rem;
    color: white;
}

.feature-card h4 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--text-primary);
}

.feature-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

.stat-item {
    padding: 20px;
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 10px;
}

.stat-label {
    font-size: 1.1rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.cta-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    color: white;
}

.cta-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 20px;
}

.cta-subtitle {
    font-size: 1.2rem;
    margin-bottom: 30px;
    opacity: 0.9;
}

.cta-actions .btn-outline-primary {
    border-color: white;
    color: white;
}

.cta-actions .btn-outline-primary:hover {
    background: white;
    color: var(--primary-color);
}

@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .feature-card {
        padding: 30px 15px;
    }
    
    .stat-number {
        font-size: 2.5rem;
    }
    
    .cta-title {
        font-size: 2rem;
    }
}
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.modern', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\ECP\pakvote\resources\views/modern-home.blade.php ENDPATH**/ ?>