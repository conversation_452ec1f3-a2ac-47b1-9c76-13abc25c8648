<!-- Modern Header Component -->
<header class="main-header">
    <!-- Top Bar -->
    <div class="top-bar">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="top-info">
                        <span class="info-item">
                            <i class="bi bi-telephone"></i>
                            <span>+92-51-111-222-333</span>
                        </span>
                        <span class="info-item">
                            <i class="bi bi-envelope"></i>
                            <span><EMAIL></span>
                        </span>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="top-actions">
                        <div class="language-switcher">
                            <div class="dropdown">
                                <button class="btn btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                    <i class="bi bi-globe"></i>
                                    <span><?php echo e(app()->getLocale() === 'ur' ? 'اردو' : 'English'); ?></span>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="?lang=en">🇺🇸 English</a></li>
                                    <li><a class="dropdown-item" href="?lang=ur">🇵🇰 اردو</a></li>
                                    <li><a class="dropdown-item" href="?lang=sd">🇵🇰 سنڌي</a></li>
                                    <li><a class="dropdown-item" href="?lang=ps">🇵🇰 پښتو</a></li>
                                </ul>
                            </div>
                        </div>
                        <div class="social-links">
                            <a href="#" class="social-link"><i class="bi bi-facebook"></i></a>
                            <a href="#" class="social-link"><i class="bi bi-twitter"></i></a>
                            <a href="#" class="social-link"><i class="bi bi-youtube"></i></a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Navigation -->
    <nav class="navbar navbar-expand-lg main-navbar">
        <div class="container-fluid">
            <!-- Brand Logo -->
            <a class="navbar-brand" href="<?php echo e(route('home')); ?>">
                <div class="brand-container">
                    <div class="logo-wrapper">
                        <div class="logo-icon">
                            <i class="bi bi-check-circle-fill"></i>
                        </div>
                        <div class="logo-text">
                            <span class="brand-name">PakVote</span>
                            <span class="brand-tagline">Election Management System</span>
                            <span class="brand-tagline-urdu urdu-text">انتخابی نظام</span>
                        </div>
                    </div>
                </div>
            </a>

            <!-- Mobile Toggle -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#mainNavbar">
                <span class="navbar-toggler-icon"></span>
            </button>

            <!-- Navigation Menu -->
            <div class="collapse navbar-collapse" id="mainNavbar">
                <ul class="navbar-nav me-auto">
                    <?php if(auth()->guard()->check()): ?>
                        @try
                            <?php if(auth()->user()->isAdmin()): ?>
                                <li class="nav-item dropdown">
                                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                        <i class="bi bi-speedometer2"></i> Admin Panel
                                    </a>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="<?php echo e(route('admin.dashboard')); ?>">
                                            <i class="bi bi-house"></i> Dashboard
                                        </a></li>
                                        <li><a class="dropdown-item" href="<?php echo e(route('admin.users')); ?>">
                                            <i class="bi bi-people"></i> Users
                                        </a></li>
                                        <li><a class="dropdown-item" href="<?php echo e(route('admin.tenants')); ?>">
                                            <i class="bi bi-building"></i> Organizations
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item" href="<?php echo e(route('admin.settings')); ?>">
                                            <i class="bi bi-gear"></i> Settings
                                        </a></li>
                                    </ul>
                                </li>
                            <?php elseif(auth()->user()->isCandidate()): ?>
                                <li class="nav-item dropdown">
                                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                        <i class="bi bi-person-badge"></i> Campaign
                                    </a>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="<?php echo e(route('candidate.dashboard')); ?>">
                                            <i class="bi bi-house"></i> Dashboard
                                        </a></li>
                                        <li><a class="dropdown-item" href="<?php echo e(route('candidate.profile')); ?>">
                                            <i class="bi bi-person"></i> Profile
                                        </a></li>
                                        <li><a class="dropdown-item" href="<?php echo e(route('candidate.voters')); ?>">
                                            <i class="bi bi-people"></i> Voters
                                        </a></li>
                                    </ul>
                                </li>
                            <?php endif; ?>
                        @catch(\Exception $e)
                            <!-- Fallback if there are any route issues -->
                            <li class="nav-item">
                                <a class="nav-link" href="/dashboard">
                                    <i class="bi bi-house"></i> Dashboard
                                </a>
                            </li>
                        @endtry
                    <?php endif; ?>
                </ul>

                <!-- Right Side Menu -->
                <ul class="navbar-nav">
                    <?php if(auth()->guard()->guest()): ?>
                        <li class="nav-item">
                            <a class="nav-link btn-outline-primary me-2" href="<?php echo e(route('login')); ?>">
                                <i class="bi bi-box-arrow-in-right"></i> Login
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link btn-primary text-white" href="<?php echo e(route('register')); ?>">
                                <i class="bi bi-person-plus"></i> Register
                            </a>
                        </li>
                    <?php else: ?>
                        <!-- Notifications -->
                        <li class="nav-item dropdown">
                            <a class="nav-link position-relative" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="bi bi-bell"></i>
                                <span class="notification-badge">3</span>
                            </a>
                            <div class="dropdown-menu dropdown-menu-end notification-dropdown">
                                <h6 class="dropdown-header">Notifications</h6>
                                <a class="dropdown-item" href="#">
                                    <div class="notification-item">
                                        <i class="bi bi-info-circle text-info"></i>
                                        <div>
                                            <strong>System Update</strong>
                                            <small class="text-muted d-block">2 minutes ago</small>
                                        </div>
                                    </div>
                                </a>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item text-center" href="#">View All Notifications</a>
                            </div>
                        </li>

                        <!-- User Menu -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle user-menu" href="#" role="button" data-bs-toggle="dropdown">
                                <div class="user-avatar">
                                    <img src="https://ui-avatars.com/api/?name=<?php echo e(urlencode(auth()->user()->name)); ?>&background=2d5a27&color=fff" 
                                         alt="Avatar" class="avatar-img">
                                </div>
                                <span class="user-name"><?php echo e(auth()->user()->name); ?></span>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end user-dropdown">
                                <li class="dropdown-header">
                                    <div class="user-info">
                                        <strong><?php echo e(auth()->user()->name); ?></strong>
                                        <small class="text-muted d-block"><?php echo e(auth()->user()->email); ?></small>
                                        <span class="badge bg-<?php echo e(auth()->user()->user_type === 'admin' ? 'danger' : 'primary'); ?> mt-1">
                                            <?php echo e(ucfirst(auth()->user()->user_type)); ?>

                                        </span>
                                    </div>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#">
                                    <i class="bi bi-person"></i> My Profile
                                </a></li>
                                <li><a class="dropdown-item" href="#">
                                    <i class="bi bi-gear"></i> Account Settings
                                </a></li>
                                <li><a class="dropdown-item" href="#">
                                    <i class="bi bi-question-circle"></i> Help & Support
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <form method="POST" action="<?php echo e(route('logout')); ?>" class="d-inline w-100">
                                        <?php echo csrf_field(); ?>
                                        <button type="submit" class="dropdown-item text-danger">
                                            <i class="bi bi-box-arrow-right"></i> Logout
                                        </button>
                                    </form>
                                </li>
                            </ul>
                        </li>

                        <!-- Organization Info -->
                        <?php if(session('current_tenant')): ?>
                            <li class="nav-item">
                                <span class="navbar-text org-info">
                                    <i class="bi bi-building"></i>
                                    <small><?php echo e(session('current_tenant')->name['en'] ?? session('current_tenant')->name); ?></small>
                                </span>
                            </li>
                        <?php endif; ?>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Breadcrumb -->
    <?php if(request()->route() && !request()->routeIs('home')): ?>
        <div class="breadcrumb-section">
            <div class="container-fluid">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('home')); ?>">Home</a></li>
                        <?php if(auth()->check()): ?>
                            <?php if(auth()->user()->isAdmin()): ?>
                                <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">Admin</a></li>
                            <?php elseif(auth()->user()->isCandidate()): ?>
                                <li class="breadcrumb-item"><a href="<?php echo e(route('candidate.dashboard')); ?>">Campaign</a></li>
                            <?php endif; ?>
                        <?php endif; ?>
                        <li class="breadcrumb-item active"><?php echo $__env->yieldContent('title', 'Page'); ?></li>
                    </ol>
                </nav>
            </div>
        </div>
    <?php endif; ?>
</header>
<?php /**PATH C:\xampp\htdocs\ECP\pakvote\resources\views/components/header.blade.php ENDPATH**/ ?>