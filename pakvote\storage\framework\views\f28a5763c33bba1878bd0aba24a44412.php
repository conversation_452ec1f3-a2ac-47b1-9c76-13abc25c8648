<!DOCTYPE html>
<html>
<head>
    <title>Test Login Debug</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h4>Login Debug Test</h4>
                    </div>
                    <div class="card-body">
                        <form id="testLoginForm">
                            <div class="mb-3">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="email" name="email" value="<EMAIL>">
                            </div>
                            <div class="mb-3">
                                <label for="password" class="form-label">Password</label>
                                <input type="password" class="form-control" id="password" name="password" value="admin123">
                            </div>
                            <button type="submit" class="btn btn-primary">Test Login</button>
                        </form>
                        
                        <div id="result" class="mt-4" style="display: none;">
                            <h5>Login Test Result:</h5>
                            <pre id="resultContent"></pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('testLoginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            
            fetch('/test-login', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                }
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('result').style.display = 'block';
                document.getElementById('resultContent').textContent = JSON.stringify(data, null, 2);
                
                if (data.status === 'success' && data.redirect_url) {
                    setTimeout(() => {
                        window.location.href = data.redirect_url;
                    }, 3000);
                }
            })
            .catch(error => {
                document.getElementById('result').style.display = 'block';
                document.getElementById('resultContent').textContent = 'Error: ' + error.message;
            });
        });
    </script>
</body>
</html>
<?php /**PATH C:\xampp\htdocs\ECP\pakvote\resources\views/test-login.blade.php ENDPATH**/ ?>