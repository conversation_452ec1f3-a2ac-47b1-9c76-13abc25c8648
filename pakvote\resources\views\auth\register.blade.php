@extends('layouts.app')

@section('title', 'Register')

@section('content')
<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card shadow">
            <div class="card-header bg-success text-white text-center">
                <h4 class="mb-0">
                    <i class="bi bi-person-plus"></i> Register for PakVote
                </h4>
                <p class="mb-0 mt-2 urdu-text">پاک ووٹ کے لیے رجسٹر کریں</p>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ route('register') }}">
                    @csrf

                    <div class="row">
                        <!-- Personal Information -->
                        <div class="col-md-6">
                            <h6 class="text-muted mb-3">
                                <i class="bi bi-person"></i> Personal Information
                            </h6>

                            <!-- Name -->
                            <div class="mb-3">
                                <label for="name" class="form-label">Full Name *</label>
                                <input type="text" 
                                       class="form-control @error('name') is-invalid @enderror" 
                                       id="name" 
                                       name="name" 
                                       value="{{ old('name') }}" 
                                       required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Email -->
                            <div class="mb-3">
                                <label for="email" class="form-label">Email Address *</label>
                                <input type="email" 
                                       class="form-control @error('email') is-invalid @enderror" 
                                       id="email" 
                                       name="email" 
                                       value="{{ old('email') }}" 
                                       required>
                                @error('email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Phone -->
                            <div class="mb-3">
                                <label for="phone" class="form-label">Phone Number</label>
                                <input type="tel" 
                                       class="form-control @error('phone') is-invalid @enderror" 
                                       id="phone" 
                                       name="phone" 
                                       value="{{ old('phone') }}" 
                                       placeholder="+92-300-1234567">
                                @error('phone')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- CNIC -->
                            <div class="mb-3">
                                <label for="cnic" class="form-label">CNIC Number *</label>
                                <input type="text" 
                                       class="form-control @error('cnic') is-invalid @enderror" 
                                       id="cnic" 
                                       name="cnic" 
                                       value="{{ old('cnic') }}" 
                                       placeholder="12345-6789012-3"
                                       maxlength="15"
                                       required>
                                @error('cnic')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Organization & Account Information -->
                        <div class="col-md-6">
                            <h6 class="text-muted mb-3">
                                <i class="bi bi-building"></i> Organization & Account
                            </h6>

                            <!-- Organization Name -->
                            <div class="mb-3">
                                <label for="organization_name" class="form-label">Organization Name *</label>
                                <input type="text" 
                                       class="form-control @error('organization_name') is-invalid @enderror" 
                                       id="organization_name" 
                                       name="organization_name" 
                                       value="{{ old('organization_name') }}" 
                                       placeholder="e.g., My Campaign Office"
                                       required>
                                @error('organization_name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>



                            <!-- User Type -->
                            <div class="mb-3">
                                <label for="user_type" class="form-label">Account Type *</label>
                                <select class="form-select @error('user_type') is-invalid @enderror" 
                                        id="user_type" 
                                        name="user_type" 
                                        required>
                                    <option value="">Select Account Type</option>
                                    <option value="candidate" {{ old('user_type') == 'candidate' ? 'selected' : '' }}>
                                        Candidate
                                    </option>
                                    <option value="agent" {{ old('user_type') == 'agent' ? 'selected' : '' }}>
                                        Campaign Agent
                                    </option>
                                    <option value="volunteer" {{ old('user_type') == 'volunteer' ? 'selected' : '' }}>
                                        Volunteer
                                    </option>
                                </select>
                                @error('user_type')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Password -->
                            <div class="mb-3">
                                <label for="password" class="form-label">Password *</label>
                                <input type="password" 
                                       class="form-control @error('password') is-invalid @enderror" 
                                       id="password" 
                                       name="password" 
                                       required>
                                @error('password')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Confirm Password -->
                            <div class="mb-3">
                                <label for="password_confirmation" class="form-label">Confirm Password *</label>
                                <input type="password" 
                                       class="form-control" 
                                       id="password_confirmation" 
                                       name="password_confirmation" 
                                       required>
                            </div>
                        </div>
                    </div>

                    <!-- Terms and Conditions -->
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="terms" required>
                        <label class="form-check-label" for="terms">
                            I agree to the <a href="#" class="text-decoration-none">Terms and Conditions</a> 
                            and <a href="#" class="text-decoration-none">Privacy Policy</a>
                        </label>
                    </div>

                    <!-- Submit Button -->
                    <div class="d-grid">
                        <button type="submit" class="btn btn-success btn-lg">
                            <i class="bi bi-person-plus"></i> Create Account
                        </button>
                    </div>
                </form>

                <hr>

                <!-- Login Link -->
                <div class="text-center">
                    <p class="mb-0">
                        Already have an account? 
                        <a href="{{ route('login') }}" class="text-decoration-none">
                            <i class="bi bi-box-arrow-in-right"></i> Login here
                        </a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .card {
        border: none;
        border-radius: 15px;
    }
    .card-header {
        border-radius: 15px 15px 0 0 !important;
    }
    .form-control:focus, .form-select:focus {
        border-color: #2d5a27;
        box-shadow: 0 0 0 0.2rem rgba(45, 90, 39, 0.25);
    }
</style>
@endpush

@push('scripts')
<script>
    // Format CNIC input
    document.getElementById('cnic').addEventListener('input', function(e) {
        let value = e.target.value.replace(/\D/g, '');
        if (value.length >= 5) {
            value = value.substring(0, 5) + '-' + value.substring(5);
        }
        if (value.length >= 13) {
            value = value.substring(0, 13) + '-' + value.substring(13, 14);
        }
        e.target.value = value;
    });


</script>
@endpush
