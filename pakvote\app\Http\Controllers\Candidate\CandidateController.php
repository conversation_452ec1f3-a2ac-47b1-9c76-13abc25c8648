<?php

namespace App\Http\Controllers\Candidate;

use App\Http\Controllers\Controller;
use App\Models\Candidate;
use App\Models\Campaign;
use App\Models\Voter;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CandidateController extends Controller
{
    /**
     * Show the candidate dashboard
     */
    public function dashboard()
    {
        $user = Auth::user();
        $tenant = session('current_tenant');

        $candidate = Candidate::where('user_id', $user->id)->first();

        $stats = [
            'total_campaigns' => $candidate ? Campaign::where('candidate_id', $candidate->id)->count() : 0,
            'total_voters' => Voter::where('tenant_id', $tenant->id)->count(),
            'active_volunteers' => $user->tenant->users()->where('user_type', 'volunteer')->where('is_active', true)->count(),
            'pending_tasks' => 0, // Placeholder for future implementation
        ];

        return view('candidate.dashboard', compact('stats', 'candidate'));
    }

    /**
     * Show candidate profile
     */
    public function profile()
    {
        $user = Auth::user();
        $candidate = Candidate::where('user_id', $user->id)->first();

        return view('candidate.profile', compact('candidate'));
    }

    /**
     * Show campaign management
     */
    public function campaign()
    {
        $user = Auth::user();
        $candidate = Candidate::where('user_id', $user->id)->first();
        $campaigns = $candidate ? Campaign::where('candidate_id', $candidate->id)->get() : collect();

        return view('candidate.campaign', compact('campaigns', 'candidate'));
    }

    /**
     * Show voters management
     */
    public function voters()
    {
        $tenant = session('current_tenant');
        $voters = Voter::where('tenant_id', $tenant->id)->paginate(20);

        return view('candidate.voters', compact('voters'));
    }

    /**
     * Show volunteers management
     */
    public function volunteers()
    {
        $tenant = session('current_tenant');
        $volunteers = $tenant->users()->where('user_type', 'volunteer')->paginate(20);

        return view('candidate.volunteers', compact('volunteers'));
    }

    /**
     * Show reports
     */
    public function reports()
    {
        return view('candidate.reports');
    }
}
