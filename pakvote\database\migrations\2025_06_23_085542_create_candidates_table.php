<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('candidates', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('user_id');
            $table->uuid('tenant_id');
            $table->uuid('political_party_id')->nullable();
            $table->uuid('constituency_id');
            $table->string('candidate_name');
            $table->string('father_name');
            $table->string('cnic', 15)->unique();
            $table->date('date_of_birth');
            $table->enum('gender', ['male', 'female', 'other']);
            $table->string('education')->nullable();
            $table->string('profession')->nullable();
            $table->text('address');
            $table->string('phone');
            $table->string('email');
            $table->string('profile_image')->nullable();
            $table->json('manifesto')->nullable(); // Translatable field
            $table->string('election_symbol');
            $table->string('symbol_image')->nullable();
            $table->enum('nomination_status', ['pending', 'approved', 'rejected', 'withdrawn'])->default('pending');
            $table->date('nomination_date')->nullable();
            $table->decimal('campaign_budget', 15, 2)->default(0);
            $table->boolean('is_active')->default(true);
            $table->string('ecp_nomination_number')->nullable()->unique();
            $table->json('social_media_links')->nullable();
            $table->timestamps();

            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('tenant_id')->references('id')->on('tenants')->onDelete('cascade');
            $table->foreign('political_party_id')->references('id')->on('political_parties')->onDelete('set null');
            $table->foreign('constituency_id')->references('id')->on('constituencies')->onDelete('cascade');

            $table->index(['tenant_id', 'constituency_id']);
            $table->index('nomination_status');
            $table->index('is_active');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('candidates');
    }
};
