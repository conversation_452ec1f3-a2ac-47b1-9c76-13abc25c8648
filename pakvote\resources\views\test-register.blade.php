<!DOCTYPE html>
<html>
<head>
    <title>Test Registration</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h4>Test Registration (No CSRF)</h4>
                    </div>
                    <div class="card-body">
                        <form id="testRegisterForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="name" class="form-label">Full Name</label>
                                        <input type="text" class="form-control" id="name" name="name" value="Test User" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="email" class="form-label">Email</label>
                                        <input type="email" class="form-control" id="email" name="email" value="<EMAIL>" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="phone" class="form-label">Phone</label>
                                        <input type="tel" class="form-control" id="phone" name="phone" value="+92-300-1234567">
                                    </div>
                                    <div class="mb-3">
                                        <label for="cnic" class="form-label">CNIC</label>
                                        <input type="text" class="form-control" id="cnic" name="cnic" value="12345-6789012-4" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="organization_name" class="form-label">Organization Name</label>
                                        <input type="text" class="form-control" id="organization_name" name="organization_name" value="Test Campaign" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="user_type" class="form-label">User Type</label>
                                        <select class="form-select" id="user_type" name="user_type" required>
                                            <option value="candidate">Candidate</option>
                                            <option value="agent">Agent</option>
                                            <option value="volunteer">Volunteer</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="password" class="form-label">Password</label>
                                        <input type="password" class="form-control" id="password" name="password" value="password123" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="password_confirmation" class="form-label">Confirm Password</label>
                                        <input type="password" class="form-control" id="password_confirmation" name="password_confirmation" value="password123" required>
                                    </div>
                                </div>
                            </div>
                            <button type="submit" class="btn btn-primary">Test Register</button>
                        </form>
                        
                        <div id="result" class="mt-4" style="display: none;">
                            <h5>Registration Test Result:</h5>
                            <pre id="resultContent"></pre>
                        </div>
                        
                        <hr>
                        <div class="mt-3">
                            <a href="{{ route('register') }}" class="btn btn-success">Go to Real Register</a>
                            <a href="{{ route('login') }}" class="btn btn-info">Go to Login</a>
                            <a href="{{ route('test.csrf') }}" class="btn btn-warning">Test CSRF</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('testRegisterForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            
            fetch('/test-register', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('result').style.display = 'block';
                document.getElementById('resultContent').textContent = JSON.stringify(data, null, 2);
            })
            .catch(error => {
                document.getElementById('result').style.display = 'block';
                document.getElementById('resultContent').textContent = 'Error: ' + error.message;
            });
        });
    </script>
</body>
</html>
