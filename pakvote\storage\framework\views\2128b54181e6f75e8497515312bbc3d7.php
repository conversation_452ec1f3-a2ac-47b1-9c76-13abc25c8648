<?php $__env->startSection('title', 'Debug Data Viewer'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">
            <i class="bi bi-database text-info"></i> Debug Data Viewer
        </h1>
        <div>
            <a href="<?php echo e(route('login')); ?>" class="btn btn-success">
                <i class="bi bi-box-arrow-in-right"></i> Go to Login
            </a>
        </div>
    </div>

    <!-- Users Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-people"></i> Users (<?php echo e($users->count()); ?>)
                    </h5>
                </div>
                <div class="card-body">
                    <?php if($users->count() > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>User Type</th>
                                        <th>Tenant</th>
                                        <th>Active</th>
                                        <th>Created</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td><small><?php echo e(substr($user->id, 0, 8)); ?>...</small></td>
                                            <td><?php echo e($user->name); ?></td>
                                            <td><?php echo e($user->email); ?></td>
                                            <td>
                                                <span class="badge bg-<?php echo e($user->user_type === 'admin' ? 'danger' : 'primary'); ?>">
                                                    <?php echo e(ucfirst($user->user_type)); ?>

                                                </span>
                                            </td>
                                            <td>
                                                <?php if($user->tenant): ?>
                                                    <?php echo e($user->tenant->name['en'] ?? $user->tenant->name); ?>

                                                <?php else: ?>
                                                    <span class="text-muted">No Tenant</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo e($user->is_active ? 'success' : 'secondary'); ?>">
                                                    <?php echo e($user->is_active ? 'Active' : 'Inactive'); ?>

                                                </span>
                                            </td>
                                            <td><?php echo e($user->created_at->format('M j, Y')); ?></td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <p class="text-muted text-center">No users found.</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Tenants Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-building"></i> Organizations/Tenants (<?php echo e($tenants->count()); ?>)
                    </h5>
                </div>
                <div class="card-body">
                    <?php if($tenants->count() > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Name</th>
                                        <th>Domain</th>
                                        <th>Plan</th>
                                        <th>Status</th>
                                        <th>Active</th>
                                        <th>Created</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $tenants; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tenant): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td><small><?php echo e(substr($tenant->id, 0, 8)); ?>...</small></td>
                                            <td><?php echo e($tenant->name['en'] ?? $tenant->name); ?></td>
                                            <td><?php echo e($tenant->domain); ?></td>
                                            <td>
                                                <span class="badge bg-<?php echo e($tenant->subscription_plan === 'enterprise' ? 'success' : ($tenant->subscription_plan === 'pro' ? 'warning' : 'secondary')); ?>">
                                                    <?php echo e(ucfirst($tenant->subscription_plan)); ?>

                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo e($tenant->subscription_status === 'active' ? 'success' : 'danger'); ?>">
                                                    <?php echo e(ucfirst($tenant->subscription_status)); ?>

                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo e($tenant->is_active ? 'success' : 'secondary'); ?>">
                                                    <?php echo e($tenant->is_active ? 'Active' : 'Inactive'); ?>

                                                </span>
                                            </td>
                                            <td><?php echo e($tenant->created_at->format('M j, Y')); ?></td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <p class="text-muted text-center">No tenants found.</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Political Parties Section -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-flag"></i> Political Parties (<?php echo e($parties->count()); ?>)
                    </h5>
                </div>
                <div class="card-body">
                    <?php if($parties->count() > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Symbol</th>
                                        <th>Short Name</th>
                                        <th>Active</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $parties; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $party): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td><?php echo e($party->name['en'] ?? $party->name); ?></td>
                                            <td><?php echo e($party->symbol); ?></td>
                                            <td><?php echo e($party->short_name); ?></td>
                                            <td>
                                                <span class="badge bg-<?php echo e($party->is_active ? 'success' : 'secondary'); ?>">
                                                    <?php echo e($party->is_active ? 'Active' : 'Inactive'); ?>

                                                </span>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <p class="text-muted text-center">No political parties found.</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Constituencies Section -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="bi bi-geo-alt"></i> Constituencies (<?php echo e($constituencies->count()); ?>)
                    </h5>
                </div>
                <div class="card-body">
                    <?php if($constituencies->count() > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Code</th>
                                        <th>Name</th>
                                        <th>Type</th>
                                        <th>Province</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $constituencies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $constituency): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td><?php echo e($constituency->code); ?></td>
                                            <td><?php echo e($constituency->name['en'] ?? $constituency->name); ?></td>
                                            <td>
                                                <span class="badge bg-primary">
                                                    <?php echo e(ucfirst($constituency->type)); ?>

                                                </span>
                                            </td>
                                            <td><?php echo e(ucfirst($constituency->province)); ?></td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <p class="text-muted text-center">No constituencies found.</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Login Instructions -->
    <div class="row">
        <div class="col-12">
            <div class="alert alert-info">
                <h4 class="alert-heading">
                    <i class="bi bi-info-circle"></i> Login Instructions
                </h4>
                <p>Use the following credentials to test the login:</p>
                <ul class="mb-0">
                    <li><strong>Email:</strong> <EMAIL></li>
                    <li><strong>Password:</strong> admin123</li>
                </ul>
                <hr>
                <a href="<?php echo e(route('login')); ?>" class="btn btn-primary">
                    <i class="bi bi-arrow-right"></i> Go to Login Page
                </a>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .table th {
        border-top: none;
    }
    .card {
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\ECP\pakvote\resources\views/debug/data.blade.php ENDPATH**/ ?>