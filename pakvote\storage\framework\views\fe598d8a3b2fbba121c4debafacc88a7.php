<?php $__env->startSection('title', 'Manage Constituencies'); ?>

<?php $__env->startSection('sidebar'); ?>
<div class="list-group list-group-flush">
    <a href="<?php echo e(route('admin.dashboard')); ?>" class="list-group-item list-group-item-action">
        <i class="bi bi-speedometer2"></i> Dashboard
    </a>
    <a href="<?php echo e(route('admin.users')); ?>" class="list-group-item list-group-item-action">
        <i class="bi bi-people"></i> Users
    </a>
    <a href="<?php echo e(route('admin.tenants')); ?>" class="list-group-item list-group-item-action">
        <i class="bi bi-building"></i> Organizations
    </a>
    <a href="<?php echo e(route('admin.parties')); ?>" class="list-group-item list-group-item-action">
        <i class="bi bi-flag"></i> Political Parties
    </a>
    <a href="<?php echo e(route('admin.constituencies')); ?>" class="list-group-item list-group-item-action active">
        <i class="bi bi-geo-alt"></i> Constituencies
    </a>
    <a href="<?php echo e(route('admin.settings')); ?>" class="list-group-item list-group-item-action">
        <i class="bi bi-gear"></i> Settings
    </a>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="bi bi-geo-alt text-warning"></i> Manage Constituencies
    </h1>
    <div>
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addConstituencyModal">
            <i class="bi bi-plus"></i> Add Constituency
        </button>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card border-warning">
            <div class="card-body text-center">
                <i class="bi bi-geo-alt display-6 text-warning"></i>
                <h4 class="mt-2"><?php echo e($constituencies->total()); ?></h4>
                <p class="text-muted mb-0">Total Constituencies</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-primary">
            <div class="card-body text-center">
                <i class="bi bi-flag display-6 text-primary"></i>
                <h4 class="mt-2"><?php echo e($constituencies->where('type', 'national')->count()); ?></h4>
                <p class="text-muted mb-0">National Assembly</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-success">
            <div class="card-body text-center">
                <i class="bi bi-building display-6 text-success"></i>
                <h4 class="mt-2"><?php echo e($constituencies->where('type', 'provincial')->count()); ?></h4>
                <p class="text-muted mb-0">Provincial Assembly</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-info">
            <div class="card-body text-center">
                <i class="bi bi-people display-6 text-info"></i>
                <h4 class="mt-2"><?php echo e($constituencies->sum('candidates_count')); ?></h4>
                <p class="text-muted mb-0">Total Candidates</p>
            </div>
        </div>
    </div>
</div>

<!-- Filter Tabs -->
<ul class="nav nav-tabs mb-3" id="constituencyTabs" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link active" id="all-tab" data-bs-toggle="tab" data-bs-target="#all" type="button" role="tab">
            All Constituencies
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="national-tab" data-bs-toggle="tab" data-bs-target="#national" type="button" role="tab">
            National Assembly
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="provincial-tab" data-bs-toggle="tab" data-bs-target="#provincial" type="button" role="tab">
            Provincial Assembly
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="local-tab" data-bs-toggle="tab" data-bs-target="#local" type="button" role="tab">
            Local Government
        </button>
    </li>
</ul>

<!-- Constituencies Table -->
<div class="card">
    <div class="card-header bg-light">
        <h5 class="mb-0">
            <i class="bi bi-list"></i> Constituencies List
        </h5>
    </div>
    <div class="card-body">
        <?php if($constituencies->count() > 0): ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>Code</th>
                            <th>Constituency</th>
                            <th>Type</th>
                            <th>Province</th>
                            <th>District</th>
                            <th>Voters</th>
                            <th>Polling Stations</th>
                            <th>Candidates</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $constituencies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $constituency): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td>
                                    <code class="bg-light px-2 py-1 rounded"><?php echo e($constituency->code); ?></code>
                                </td>
                                <td>
                                    <div>
                                        <h6 class="mb-0"><?php echo e($constituency->name['en'] ?? $constituency->name); ?></h6>
                                        <?php if($constituency->ecp_code): ?>
                                            <small class="text-muted">ECP: <?php echo e($constituency->ecp_code); ?></small>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo e($constituency->type === 'national' ? 'primary' : ($constituency->type === 'provincial' ? 'success' : 'info')); ?>">
                                        <?php echo e(ucfirst($constituency->type)); ?>

                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-secondary"><?php echo e(ucfirst($constituency->province)); ?></span>
                                </td>
                                <td>
                                    <div>
                                        <small class="d-block"><?php echo e($constituency->district); ?></small>
                                        <?php if($constituency->tehsil): ?>
                                            <small class="text-muted"><?php echo e($constituency->tehsil); ?></small>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <div class="text-center">
                                        <strong><?php echo e(number_format($constituency->total_voters)); ?></strong>
                                        <br>
                                        <small class="text-muted">voters</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="text-center">
                                        <strong><?php echo e($constituency->total_polling_stations); ?></strong>
                                        <br>
                                        <small class="text-muted">stations</small>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-info"><?php echo e($constituency->candidates_count ?? 0); ?></span>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo e($constituency->is_active ? 'success' : 'secondary'); ?>">
                                        <?php echo e($constituency->is_active ? 'Active' : 'Inactive'); ?>

                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button type="button" class="btn btn-outline-primary" title="Edit">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-info" title="View Candidates">
                                            <i class="bi bi-people"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-success" title="View Map">
                                            <i class="bi bi-map"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-4">
                <?php echo e($constituencies->links()); ?>

            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <i class="bi bi-geo-alt display-1 text-muted"></i>
                <h4 class="text-muted mt-3">No Constituencies Found</h4>
                <p class="text-muted">Start by adding your first constituency.</p>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addConstituencyModal">
                    <i class="bi bi-plus"></i> Add Constituency
                </button>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Add Constituency Modal -->
<div class="modal fade" id="addConstituencyModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Constituency</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="constituency_name" class="form-label">Constituency Name (English)</label>
                                <input type="text" class="form-control" id="constituency_name" required>
                            </div>
                            <div class="mb-3">
                                <label for="constituency_name_urdu" class="form-label">Constituency Name (Urdu)</label>
                                <input type="text" class="form-control urdu-text" id="constituency_name_urdu" dir="rtl">
                            </div>
                            <div class="mb-3">
                                <label for="code" class="form-label">Constituency Code</label>
                                <input type="text" class="form-control" id="code" placeholder="e.g., NA-1, PP-1" required>
                            </div>
                            <div class="mb-3">
                                <label for="type" class="form-label">Type</label>
                                <select class="form-select" id="type" required>
                                    <option value="">Select Type</option>
                                    <option value="national">National Assembly</option>
                                    <option value="provincial">Provincial Assembly</option>
                                    <option value="local">Local Government</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="province" class="form-label">Province</label>
                                <select class="form-select" id="province" required>
                                    <option value="">Select Province</option>
                                    <option value="punjab">Punjab</option>
                                    <option value="sindh">Sindh</option>
                                    <option value="kpk">Khyber Pakhtunkhwa</option>
                                    <option value="balochistan">Balochistan</option>
                                    <option value="islamabad">Islamabad</option>
                                    <option value="gilgit_baltistan">Gilgit-Baltistan</option>
                                    <option value="ajk">Azad Jammu & Kashmir</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="district" class="form-label">District</label>
                                <input type="text" class="form-control" id="district" required>
                            </div>
                            <div class="mb-3">
                                <label for="tehsil" class="form-label">Tehsil</label>
                                <input type="text" class="form-control" id="tehsil">
                            </div>
                            <div class="mb-3">
                                <label for="total_voters" class="form-label">Total Voters</label>
                                <input type="number" class="form-control" id="total_voters" min="0">
                            </div>
                            <div class="mb-3">
                                <label for="total_polling_stations" class="form-label">Total Polling Stations</label>
                                <input type="number" class="form-control" id="total_polling_stations" min="0">
                            </div>
                            <div class="mb-3">
                                <label for="ecp_code" class="form-label">ECP Code</label>
                                <input type="text" class="form-control" id="ecp_code">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="area_description" class="form-label">Area Description</label>
                        <textarea class="form-control" id="area_description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Constituency</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .table th {
        border-top: none;
        font-weight: 600;
    }
    .card {
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .urdu-text {
        font-family: 'Noto Nastaliq Urdu', serif;
    }
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\ECP\pakvote\resources\views/admin/constituencies.blade.php ENDPATH**/ ?>