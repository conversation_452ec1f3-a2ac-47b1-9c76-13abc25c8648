<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <title><?php echo $__env->yieldContent('title', 'PakVote'); ?> - Election Management System</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Nastaliq+Urdu:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Modern Components CSS -->
    <link href="<?php echo e(asset('css/modern-components.css')); ?>" rel="stylesheet">

    <?php echo $__env->yieldPushContent('styles'); ?>
</head>
<body>
    <!-- Header Component -->
    <?php echo $__env->make('components.header', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <div class="app-container">
        <?php if (! empty(trim($__env->yieldContent('sidebar')))): ?>
            <!-- Modern Sidebar -->
            <?php echo $__env->make('components.sidebar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            
            <!-- Main Content with Sidebar -->
            <div class="main-content with-sidebar">
                <div class="content-wrapper">
                    <!-- Flash Messages -->
                    <?php if(session('success')): ?>
                        <div class="alert alert-success alert-dismissible fade show modern-alert" role="alert">
                            <div class="alert-content">
                                <i class="bi bi-check-circle alert-icon"></i>
                                <div class="alert-text">
                                    <strong>Success!</strong>
                                    <p><?php echo e(session('success')); ?></p>
                                </div>
                            </div>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if(session('error')): ?>
                        <div class="alert alert-danger alert-dismissible fade show modern-alert" role="alert">
                            <div class="alert-content">
                                <i class="bi bi-exclamation-triangle alert-icon"></i>
                                <div class="alert-text">
                                    <strong>Error!</strong>
                                    <p><?php echo e(session('error')); ?></p>
                                </div>
                            </div>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if($errors->any()): ?>
                        <div class="alert alert-danger alert-dismissible fade show modern-alert" role="alert">
                            <div class="alert-content">
                                <i class="bi bi-exclamation-triangle alert-icon"></i>
                                <div class="alert-text">
                                    <strong>Please fix the following errors:</strong>
                                    <ul class="mb-0 mt-2">
                                        <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <li><?php echo e($error); ?></li>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </ul>
                                </div>
                            </div>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <!-- Page Content -->
                    <main class="page-content">
                        <?php echo $__env->yieldContent('content'); ?>
                    </main>
                </div>
            </div>
        <?php else: ?>
            <!-- Main Content without Sidebar -->
            <div class="main-content">
                <div class="container-fluid">
                    <!-- Flash Messages -->
                    <?php if(session('success')): ?>
                        <div class="alert alert-success alert-dismissible fade show modern-alert" role="alert">
                            <div class="alert-content">
                                <i class="bi bi-check-circle alert-icon"></i>
                                <div class="alert-text">
                                    <strong>Success!</strong>
                                    <p><?php echo e(session('success')); ?></p>
                                </div>
                            </div>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if(session('error')): ?>
                        <div class="alert alert-danger alert-dismissible fade show modern-alert" role="alert">
                            <div class="alert-content">
                                <i class="bi bi-exclamation-triangle alert-icon"></i>
                                <div class="alert-text">
                                    <strong>Error!</strong>
                                    <p><?php echo e(session('error')); ?></p>
                                </div>
                            </div>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if($errors->any()): ?>
                        <div class="alert alert-danger alert-dismissible fade show modern-alert" role="alert">
                            <div class="alert-content">
                                <i class="bi bi-exclamation-triangle alert-icon"></i>
                                <div class="alert-text">
                                    <strong>Please fix the following errors:</strong>
                                    <ul class="mb-0 mt-2">
                                        <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <li><?php echo e($error); ?></li>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </ul>
                                </div>
                            </div>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <!-- Page Content -->
                    <main class="page-content">
                        <?php echo $__env->yieldContent('content'); ?>
                    </main>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- Footer Component -->
    <?php echo $__env->make('components.footer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Modern Components JS -->
    <script>
        // Sidebar Toggle Functionality
        document.addEventListener('DOMContentLoaded', function() {
            const sidebar = document.getElementById('sidebar');
            const sidebarToggle = document.getElementById('sidebarToggle');
            const sidebarOverlay = document.getElementById('sidebarOverlay');
            
            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('collapsed');
                });
            }
            
            // Mobile sidebar toggle
            if (window.innerWidth <= 768) {
                if (sidebarOverlay) {
                    sidebarOverlay.addEventListener('click', function() {
                        sidebar.classList.remove('show');
                        sidebarOverlay.classList.remove('show');
                    });
                }
            }
            
            // Auto-hide alerts after 5 seconds
            const alerts = document.querySelectorAll('.modern-alert');
            alerts.forEach(alert => {
                setTimeout(() => {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }, 5000);
            });
        });
    </script>
    
    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH C:\xampp\htdocs\ECP\pakvote\resources\views/layouts/modern.blade.php ENDPATH**/ ?>