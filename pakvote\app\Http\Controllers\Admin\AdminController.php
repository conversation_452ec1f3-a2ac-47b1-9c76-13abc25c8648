<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Tenant;
use App\Models\Candidate;
use App\Models\PoliticalParty;
use App\Models\Constituency;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

class AdminController extends Controller
{
    /**
     * Show the admin dashboard
     */
    public function dashboard()
    {
        $stats = [
            'total_tenants' => Tenant::count(),
            'active_tenants' => Tenant::where('is_active', true)->count(),
            'total_users' => User::count(),
            'total_candidates' => Candidate::count(),
            'total_parties' => PoliticalParty::count(),
            'total_constituencies' => Constituency::count(),
        ];

        $recent_tenants = Tenant::latest()->take(5)->get();
        $recent_users = User::latest()->take(5)->get();

        return view('admin.dashboard', compact('stats', 'recent_tenants', 'recent_users'));
    }

    /**
     * Show users management
     */
    public function users()
    {
        $users = User::with('tenant')->paginate(20);
        return view('admin.users', compact('users'));
    }

    /**
     * Store a new user
     */
    public function storeUser(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users',
            'password' => 'required|string|min:8',
            'phone' => 'nullable|string|max:20',
            'cnic' => 'nullable|string|size:15|unique:users',
            'user_type' => 'required|in:admin,candidate,agent,volunteer',
            'tenant_id' => 'nullable|exists:tenants,id',
        ]);

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'phone' => $request->phone,
            'cnic' => $request->cnic,
            'user_type' => $request->user_type,
            'tenant_id' => $request->tenant_id,
        ]);

        $user->assignRole($request->user_type);

        return redirect()->route('admin.users')->with('success', 'User created successfully!');
    }

    /**
     * Show edit user form
     */
    public function editUser(User $user)
    {
        $tenants = Tenant::where('is_active', true)->get();
        return view('admin.edit-user', compact('user', 'tenants'));
    }

    /**
     * Update user
     */
    public function updateUser(Request $request, User $user)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email,' . $user->id,
            'phone' => 'nullable|string|max:20',
            'cnic' => 'nullable|string|size:15|unique:users,cnic,' . $user->id,
            'user_type' => 'required|in:admin,candidate,agent,volunteer',
            'tenant_id' => 'nullable|exists:tenants,id',
        ]);

        $user->update([
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'cnic' => $request->cnic,
            'user_type' => $request->user_type,
            'tenant_id' => $request->tenant_id,
            'is_active' => $request->has('is_active'),
        ]);

        // Update password if provided
        if ($request->filled('password')) {
            $user->update(['password' => Hash::make($request->password)]);
        }

        // Update role
        $user->syncRoles([$request->user_type]);

        return redirect()->route('admin.users')->with('success', 'User updated successfully!');
    }

    /**
     * Delete user
     */
    public function deleteUser(User $user)
    {
        // Prevent deleting super admin
        if ($user->hasRole('super_admin')) {
            return redirect()->route('admin.users')->with('error', 'Cannot delete super admin user!');
        }

        $user->delete();
        return redirect()->route('admin.users')->with('success', 'User deleted successfully!');
    }

    /**
     * Show tenants management
     */
    public function tenants()
    {
        $tenants = Tenant::withCount('users')->paginate(20);
        return view('admin.tenants', compact('tenants'));
    }

    /**
     * Store a new tenant
     */
    public function storeTenant(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'domain' => 'required|string|max:255|unique:tenants',
            'contact_email' => 'required|email',
            'contact_phone' => 'nullable|string|max:20',
            'subscription_plan' => 'required|in:free,pro,enterprise',
        ]);

        Tenant::create([
            'name' => ['en' => $request->name],
            'domain' => $request->domain,
            'contact_email' => $request->contact_email,
            'contact_phone' => $request->contact_phone,
            'subscription_plan' => $request->subscription_plan,
            'subscription_status' => Tenant::STATUS_ACTIVE,
        ]);

        return redirect()->route('admin.tenants')->with('success', 'Organization created successfully!');
    }

    /**
     * Show edit tenant form
     */
    public function editTenant(Tenant $tenant)
    {
        return view('admin.edit-tenant', compact('tenant'));
    }

    /**
     * Update tenant
     */
    public function updateTenant(Request $request, Tenant $tenant)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'domain' => 'required|string|max:255|unique:tenants,domain,' . $tenant->id,
            'contact_email' => 'required|email',
            'contact_phone' => 'nullable|string|max:20',
            'subscription_plan' => 'required|in:free,pro,enterprise',
            'subscription_status' => 'required|in:active,inactive,suspended,cancelled',
        ]);

        $tenant->update([
            'name' => ['en' => $request->name],
            'domain' => $request->domain,
            'contact_email' => $request->contact_email,
            'contact_phone' => $request->contact_phone,
            'subscription_plan' => $request->subscription_plan,
            'subscription_status' => $request->subscription_status,
            'is_active' => $request->has('is_active'),
        ]);

        return redirect()->route('admin.tenants')->with('success', 'Organization updated successfully!');
    }

    /**
     * Delete tenant
     */
    public function deleteTenant(Tenant $tenant)
    {
        // Check if tenant has users
        if ($tenant->users()->count() > 0) {
            return redirect()->route('admin.tenants')->with('error', 'Cannot delete organization with existing users!');
        }

        $tenant->delete();
        return redirect()->route('admin.tenants')->with('success', 'Organization deleted successfully!');
    }

    /**
     * Show parties management
     */
    public function parties()
    {
        $parties = PoliticalParty::withCount('candidates')->paginate(20);
        return view('admin.parties', compact('parties'));
    }

    /**
     * Store a new party
     */
    public function storeParty(Request $request)
    {
        $request->validate([
            'name_en' => 'required|string|max:255',
            'name_ur' => 'nullable|string|max:255',
            'short_name' => 'required|string|max:50',
            'symbol' => 'required|string|max:100|unique:political_parties',
            'contact_email' => 'nullable|email',
            'contact_phone' => 'nullable|string|max:20',
            'website' => 'nullable|url',
            'ecp_registration_number' => 'nullable|string|unique:political_parties',
            'party_head' => 'nullable|string|max:255',
            'headquarters' => 'nullable|string|max:255',
            'founded_date' => 'nullable|date',
        ]);

        PoliticalParty::create([
            'name' => [
                'en' => $request->name_en,
                'ur' => $request->name_ur,
            ],
            'short_name' => $request->short_name,
            'symbol' => $request->symbol,
            'contact_email' => $request->contact_email,
            'contact_phone' => $request->contact_phone,
            'website' => $request->website,
            'ecp_registration_number' => $request->ecp_registration_number,
            'party_head' => $request->party_head,
            'headquarters' => $request->headquarters,
            'founded_date' => $request->founded_date,
        ]);

        return redirect()->route('admin.parties')->with('success', 'Political party created successfully!');
    }

    /**
     * Show edit party form
     */
    public function editParty(PoliticalParty $party)
    {
        return view('admin.edit-party', compact('party'));
    }

    /**
     * Update party
     */
    public function updateParty(Request $request, PoliticalParty $party)
    {
        $request->validate([
            'name_en' => 'required|string|max:255',
            'name_ur' => 'nullable|string|max:255',
            'short_name' => 'required|string|max:50',
            'symbol' => 'required|string|max:100|unique:political_parties,symbol,' . $party->id,
            'contact_email' => 'nullable|email',
            'contact_phone' => 'nullable|string|max:20',
            'website' => 'nullable|url',
            'ecp_registration_number' => 'nullable|string|unique:political_parties,ecp_registration_number,' . $party->id,
            'party_head' => 'nullable|string|max:255',
            'headquarters' => 'nullable|string|max:255',
            'founded_date' => 'nullable|date',
        ]);

        $party->update([
            'name' => [
                'en' => $request->name_en,
                'ur' => $request->name_ur,
            ],
            'short_name' => $request->short_name,
            'symbol' => $request->symbol,
            'contact_email' => $request->contact_email,
            'contact_phone' => $request->contact_phone,
            'website' => $request->website,
            'ecp_registration_number' => $request->ecp_registration_number,
            'party_head' => $request->party_head,
            'headquarters' => $request->headquarters,
            'founded_date' => $request->founded_date,
            'is_active' => $request->has('is_active'),
        ]);

        return redirect()->route('admin.parties')->with('success', 'Political party updated successfully!');
    }

    /**
     * Delete party
     */
    public function deleteParty(PoliticalParty $party)
    {
        // Check if party has candidates
        if ($party->candidates()->count() > 0) {
            return redirect()->route('admin.parties')->with('error', 'Cannot delete party with existing candidates!');
        }

        $party->delete();
        return redirect()->route('admin.parties')->with('success', 'Political party deleted successfully!');
    }

    /**
     * Show constituencies management
     */
    public function constituencies()
    {
        $constituencies = Constituency::withCount('candidates')->paginate(20);
        return view('admin.constituencies', compact('constituencies'));
    }

    /**
     * Store a new constituency
     */
    public function storeConstituency(Request $request)
    {
        $request->validate([
            'name_en' => 'required|string|max:255',
            'name_ur' => 'nullable|string|max:255',
            'code' => 'required|string|max:20|unique:constituencies',
            'type' => 'required|in:national,provincial,local',
            'province' => 'required|in:punjab,sindh,kpk,balochistan,islamabad,gilgit_baltistan,ajk',
            'district' => 'required|string|max:100',
            'tehsil' => 'nullable|string|max:100',
            'total_voters' => 'nullable|integer|min:0',
            'total_polling_stations' => 'nullable|integer|min:0',
            'ecp_code' => 'nullable|string|unique:constituencies',
            'area_description' => 'nullable|string',
        ]);

        Constituency::create([
            'name' => [
                'en' => $request->name_en,
                'ur' => $request->name_ur,
            ],
            'code' => $request->code,
            'type' => $request->type,
            'province' => $request->province,
            'district' => $request->district,
            'tehsil' => $request->tehsil,
            'total_voters' => $request->total_voters ?? 0,
            'total_polling_stations' => $request->total_polling_stations ?? 0,
            'ecp_code' => $request->ecp_code,
            'area_description' => $request->area_description ? ['en' => $request->area_description] : null,
        ]);

        return redirect()->route('admin.constituencies')->with('success', 'Constituency created successfully!');
    }

    /**
     * Show edit constituency form
     */
    public function editConstituency(Constituency $constituency)
    {
        return view('admin.edit-constituency', compact('constituency'));
    }

    /**
     * Update constituency
     */
    public function updateConstituency(Request $request, Constituency $constituency)
    {
        $request->validate([
            'name_en' => 'required|string|max:255',
            'name_ur' => 'nullable|string|max:255',
            'code' => 'required|string|max:20|unique:constituencies,code,' . $constituency->id,
            'type' => 'required|in:national,provincial,local',
            'province' => 'required|in:punjab,sindh,kpk,balochistan,islamabad,gilgit_baltistan,ajk',
            'district' => 'required|string|max:100',
            'tehsil' => 'nullable|string|max:100',
            'total_voters' => 'nullable|integer|min:0',
            'total_polling_stations' => 'nullable|integer|min:0',
            'ecp_code' => 'nullable|string|unique:constituencies,ecp_code,' . $constituency->id,
            'area_description' => 'nullable|string',
        ]);

        $constituency->update([
            'name' => [
                'en' => $request->name_en,
                'ur' => $request->name_ur,
            ],
            'code' => $request->code,
            'type' => $request->type,
            'province' => $request->province,
            'district' => $request->district,
            'tehsil' => $request->tehsil,
            'total_voters' => $request->total_voters ?? 0,
            'total_polling_stations' => $request->total_polling_stations ?? 0,
            'ecp_code' => $request->ecp_code,
            'area_description' => $request->area_description ? ['en' => $request->area_description] : null,
            'is_active' => $request->has('is_active'),
        ]);

        return redirect()->route('admin.constituencies')->with('success', 'Constituency updated successfully!');
    }

    /**
     * Delete constituency
     */
    public function deleteConstituency(Constituency $constituency)
    {
        // Check if constituency has candidates
        if ($constituency->candidates()->count() > 0) {
            return redirect()->route('admin.constituencies')->with('error', 'Cannot delete constituency with existing candidates!');
        }

        $constituency->delete();
        return redirect()->route('admin.constituencies')->with('success', 'Constituency deleted successfully!');
    }

    /**
     * Show system settings
     */
    public function settings()
    {
        return view('admin.settings');
    }
}
