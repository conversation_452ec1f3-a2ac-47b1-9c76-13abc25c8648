<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Tenant;
use App\Models\Candidate;
use App\Models\PoliticalParty;
use App\Models\Constituency;
use Illuminate\Http\Request;

class AdminController extends Controller
{
    /**
     * Show the admin dashboard
     */
    public function dashboard()
    {
        $stats = [
            'total_tenants' => Tenant::count(),
            'active_tenants' => Tenant::where('is_active', true)->count(),
            'total_users' => User::count(),
            'total_candidates' => Candidate::count(),
            'total_parties' => PoliticalParty::count(),
            'total_constituencies' => Constituency::count(),
        ];

        $recent_tenants = Tenant::latest()->take(5)->get();
        $recent_users = User::latest()->take(5)->get();

        return view('admin.dashboard', compact('stats', 'recent_tenants', 'recent_users'));
    }

    /**
     * Show users management
     */
    public function users()
    {
        $users = User::with('tenant')->paginate(20);
        return view('admin.users', compact('users'));
    }

    /**
     * Show tenants management
     */
    public function tenants()
    {
        $tenants = Tenant::withCount('users')->paginate(20);
        return view('admin.tenants', compact('tenants'));
    }

    /**
     * Show parties management
     */
    public function parties()
    {
        $parties = PoliticalParty::withCount('candidates')->paginate(20);
        return view('admin.parties', compact('parties'));
    }

    /**
     * Show constituencies management
     */
    public function constituencies()
    {
        $constituencies = Constituency::withCount('candidates')->paginate(20);
        return view('admin.constituencies', compact('constituencies'));
    }

    /**
     * Show system settings
     */
    public function settings()
    {
        return view('admin.settings');
    }
}
