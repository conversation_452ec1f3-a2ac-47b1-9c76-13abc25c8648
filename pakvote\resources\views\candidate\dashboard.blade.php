@extends('layouts.app')

@section('title', 'Candidate Dashboard')

@section('sidebar')
<div class="list-group list-group-flush">
    <a href="{{ route('candidate.dashboard') }}" class="list-group-item list-group-item-action active">
        <i class="bi bi-speedometer2"></i> Dashboard
    </a>
    <a href="{{ route('candidate.profile') }}" class="list-group-item list-group-item-action">
        <i class="bi bi-person"></i> Profile
    </a>
    <a href="{{ route('candidate.campaign') }}" class="list-group-item list-group-item-action">
        <i class="bi bi-megaphone"></i> Campaign
    </a>
    <a href="{{ route('candidate.voters') }}" class="list-group-item list-group-item-action">
        <i class="bi bi-people"></i> Voters
    </a>
    <a href="{{ route('candidate.volunteers') }}" class="list-group-item list-group-item-action">
        <i class="bi bi-person-hearts"></i> Volunteers
    </a>
    <a href="{{ route('candidate.reports') }}" class="list-group-item list-group-item-action">
        <i class="bi bi-graph-up"></i> Reports
    </a>
</div>
@endsection

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="bi bi-person-badge text-success"></i> Candidate Dashboard
    </h1>
    <div class="text-muted">
        <i class="bi bi-calendar"></i> {{ now()->format('l, F j, Y') }}
    </div>
</div>

<!-- Welcome Message -->
@if(!$candidate)
    <div class="alert alert-info" role="alert">
        <h4 class="alert-heading">
            <i class="bi bi-info-circle"></i> Welcome to PakVote!
        </h4>
        <p>You haven't set up your candidate profile yet. Complete your profile to start managing your election campaign.</p>
        <hr>
        <a href="{{ route('candidate.profile') }}" class="btn btn-primary">
            <i class="bi bi-person-plus"></i> Complete Profile
        </a>
    </div>
@else
    <div class="alert alert-success" role="alert">
        <h4 class="alert-heading">
            <i class="bi bi-check-circle"></i> Welcome back, {{ $candidate->candidate_name }}!
        </h4>
        <p class="mb-0">
            Candidate for {{ $candidate->constituency->name['en'] ?? 'N/A' }} 
            @if($candidate->politicalParty)
                - {{ $candidate->politicalParty->name['en'] ?? $candidate->politicalParty->name }}
            @endif
        </p>
    </div>
@endif

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card border-primary">
            <div class="card-body text-center">
                <i class="bi bi-megaphone display-4 text-primary"></i>
                <h3 class="mt-2">{{ $stats['total_campaigns'] }}</h3>
                <p class="text-muted mb-0">Active Campaigns</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card border-info">
            <div class="card-body text-center">
                <i class="bi bi-people display-4 text-info"></i>
                <h3 class="mt-2">{{ number_format($stats['total_voters']) }}</h3>
                <p class="text-muted mb-0">Registered Voters</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card border-success">
            <div class="card-body text-center">
                <i class="bi bi-person-hearts display-4 text-success"></i>
                <h3 class="mt-2">{{ $stats['active_volunteers'] }}</h3>
                <p class="text-muted mb-0">Active Volunteers</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card border-warning">
            <div class="card-body text-center">
                <i class="bi bi-list-task display-4 text-warning"></i>
                <h3 class="mt-2">{{ $stats['pending_tasks'] }}</h3>
                <p class="text-muted mb-0">Pending Tasks</p>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="bi bi-lightning"></i> Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="{{ route('candidate.profile') }}" class="btn btn-outline-primary w-100">
                            <i class="bi bi-person-gear"></i> Update Profile
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ route('candidate.campaign') }}" class="btn btn-outline-info w-100">
                            <i class="bi bi-megaphone-fill"></i> Manage Campaign
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ route('candidate.voters') }}" class="btn btn-outline-success w-100">
                            <i class="bi bi-person-plus-fill"></i> Add Voters
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ route('candidate.volunteers') }}" class="btn btn-outline-warning w-100">
                            <i class="bi bi-people-fill"></i> Recruit Volunteers
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Campaign Status -->
@if($candidate)
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-light">
                <h5 class="mb-0">
                    <i class="bi bi-info-circle"></i> Campaign Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>Candidate Name:</strong> {{ $candidate->candidate_name }}</p>
                        <p><strong>CNIC:</strong> {{ $candidate->cnic }}</p>
                        <p><strong>Constituency:</strong> {{ $candidate->constituency->name['en'] ?? 'N/A' }}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Political Party:</strong> 
                            @if($candidate->politicalParty)
                                {{ $candidate->politicalParty->name['en'] ?? $candidate->politicalParty->name }}
                            @else
                                Independent
                            @endif
                        </p>
                        <p><strong>Election Symbol:</strong> {{ $candidate->election_symbol }}</p>
                        <p><strong>Nomination Status:</strong> 
                            <span class="badge bg-{{ $candidate->nomination_status === 'approved' ? 'success' : ($candidate->nomination_status === 'pending' ? 'warning' : 'danger') }}">
                                {{ ucfirst($candidate->nomination_status) }}
                            </span>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header bg-light">
                <h5 class="mb-0">
                    <i class="bi bi-calendar-event"></i> Upcoming Events
                </h5>
            </div>
            <div class="card-body">
                <p class="text-muted text-center">No upcoming events scheduled.</p>
                <div class="d-grid">
                    <a href="{{ route('candidate.campaign') }}" class="btn btn-outline-primary btn-sm">
                        <i class="bi bi-plus"></i> Schedule Event
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endif

<!-- Getting Started Guide -->
@if(!$candidate)
<div class="row mt-4">
    <div class="col-12">
        <div class="card border-info">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="bi bi-lightbulb"></i> Getting Started Guide
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 text-center mb-3">
                        <div class="bg-light rounded p-3">
                            <i class="bi bi-person-plus display-4 text-primary"></i>
                            <h6 class="mt-2">1. Complete Profile</h6>
                            <p class="small text-muted">Set up your candidate information</p>
                        </div>
                    </div>
                    <div class="col-md-3 text-center mb-3">
                        <div class="bg-light rounded p-3">
                            <i class="bi bi-megaphone display-4 text-info"></i>
                            <h6 class="mt-2">2. Create Campaign</h6>
                            <p class="small text-muted">Start your election campaign</p>
                        </div>
                    </div>
                    <div class="col-md-3 text-center mb-3">
                        <div class="bg-light rounded p-3">
                            <i class="bi bi-people display-4 text-success"></i>
                            <h6 class="mt-2">3. Add Voters</h6>
                            <p class="small text-muted">Import your voter database</p>
                        </div>
                    </div>
                    <div class="col-md-3 text-center mb-3">
                        <div class="bg-light rounded p-3">
                            <i class="bi bi-graph-up display-4 text-warning"></i>
                            <h6 class="mt-2">4. Track Progress</h6>
                            <p class="small text-muted">Monitor campaign analytics</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endif
@endsection

@push('styles')
<style>
    .card {
        transition: transform 0.2s;
    }
    .card:hover {
        transform: translateY(-2px);
    }
    .display-4 {
        font-size: 2.5rem;
    }
</style>
@endpush
