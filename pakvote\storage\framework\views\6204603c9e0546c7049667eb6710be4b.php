<?php $__env->startSection('title', 'Admin Dashboard'); ?>

<?php $__env->startSection('sidebar'); ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="perfect-dashboard">
    <!-- Hero Welcome Section -->
    <div class="hero-welcome">
        <div class="hero-background">
            <div class="hero-pattern"></div>
            <div class="hero-gradient"></div>
        </div>
        <div class="hero-content">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <div class="welcome-text">
                        <div class="greeting-badge">
                            <i class="bi bi-sun"></i>
                            <span>Good <?php echo e(now()->format('H') < 12 ? 'Morning' : (now()->format('H') < 17 ? 'Afternoon' : 'Evening')); ?></span>
                        </div>
                        <h1 class="hero-title">
                            Welcome back, <span class="highlight"><?php echo e(auth()->user()->name); ?></span>!
                        </h1>
                        <p class="hero-subtitle">
                            <i class="bi bi-shield-check text-success"></i>
                            Your election management system is running smoothly. Here's your overview for today.
                        </p>
                        <div class="hero-stats">
                            <div class="mini-stat">
                                <span class="mini-stat-value"><?php echo e(\App\Models\User::count()); ?></span>
                                <span class="mini-stat-label">Total Users</span>
                            </div>
                            <div class="mini-stat">
                                <span class="mini-stat-value"><?php echo e(\App\Models\Tenant::where('is_active', true)->count()); ?></span>
                                <span class="mini-stat-label">Active Organizations</span>
                            </div>
                            <div class="mini-stat">
                                <span class="mini-stat-value">99.9%</span>
                                <span class="mini-stat-label">System Uptime</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="hero-visual">
                        <div class="dashboard-clock">
                            <div class="clock-face">
                                <div class="clock-time"><?php echo e(now()->format('H:i')); ?></div>
                                <div class="clock-date"><?php echo e(now()->format('M j, Y')); ?></div>
                                <div class="clock-day"><?php echo e(now()->format('l')); ?></div>
                            </div>
                            <div class="clock-rings">
                                <div class="ring ring-1"></div>
                                <div class="ring ring-2"></div>
                                <div class="ring ring-3"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Perfect Statistics Grid -->
    <div class="stats-grid">
        <div class="stat-card stat-card-primary" data-aos="fade-up" data-aos-delay="100">
            <div class="stat-card-header">
                <div class="stat-icon">
                    <i class="bi bi-people"></i>
                </div>
                <div class="stat-trend positive">
                    <i class="bi bi-arrow-up"></i>
                    <span>+12%</span>
                </div>
            </div>
            <div class="stat-content">
                <div class="stat-number"><?php echo e(number_format(\App\Models\User::count() ?? 0)); ?></div>
                <div class="stat-label">Total Users</div>
                <div class="stat-details">
                    <div class="detail-item">
                        <span class="detail-dot active"></span>
                        <span>Active: <?php echo e(\App\Models\User::where('is_active', true)->count() ?? 0); ?></span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-dot inactive"></span>
                        <span>Inactive: <?php echo e(\App\Models\User::where('is_active', false)->count() ?? 0); ?></span>
                    </div>
                </div>
            </div>
            <div class="stat-footer">
                <a href="<?php echo e(route('admin.users')); ?>" class="stat-link">
                    <span>Manage Users</span>
                    <i class="bi bi-arrow-right"></i>
                </a>
            </div>
        </div>

        <div class="stat-card stat-card-success" data-aos="fade-up" data-aos-delay="200">
            <div class="stat-card-header">
                <div class="stat-icon">
                    <i class="bi bi-building"></i>
                </div>
                <div class="stat-trend positive">
                    <i class="bi bi-arrow-up"></i>
                    <span>+8%</span>
                </div>
            </div>
            <div class="stat-content">
                <div class="stat-number"><?php echo e(number_format(\App\Models\Tenant::count() ?? 0)); ?></div>
                <div class="stat-label">Organizations</div>
                <div class="stat-details">
                    <div class="detail-item">
                        <span class="detail-dot active"></span>
                        <span>Active: <?php echo e(\App\Models\Tenant::where('is_active', true)->count() ?? 0); ?></span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-dot pro"></span>
                        <span>Pro Plans: <?php echo e(\App\Models\Tenant::where('subscription_plan', 'pro')->count() ?? 0); ?></span>
                    </div>
                </div>
            </div>
            <div class="stat-footer">
                <a href="<?php echo e(route('admin.tenants')); ?>" class="stat-link">
                    <span>Manage Organizations</span>
                    <i class="bi bi-arrow-right"></i>
                </a>
            </div>
        </div>

        <div class="stat-card stat-card-warning" data-aos="fade-up" data-aos-delay="300">
            <div class="stat-card-header">
                <div class="stat-icon">
                    <i class="bi bi-flag"></i>
                </div>
                <div class="stat-trend positive">
                    <i class="bi bi-arrow-up"></i>
                    <span>+5%</span>
                </div>
            </div>
            <div class="stat-content">
                <div class="stat-number"><?php echo e(number_format(\App\Models\PoliticalParty::count() ?? 0)); ?></div>
                <div class="stat-label">Political Parties</div>
                <div class="stat-details">
                    <div class="detail-item">
                        <span class="detail-dot active"></span>
                        <span>Active: <?php echo e(\App\Models\PoliticalParty::where('is_active', true)->count() ?? 0); ?></span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-dot verified"></span>
                        <span>ECP Registered: <?php echo e(\App\Models\PoliticalParty::whereNotNull('ecp_registration_number')->count() ?? 0); ?></span>
                    </div>
                </div>
            </div>
            <div class="stat-footer">
                <a href="<?php echo e(route('admin.parties')); ?>" class="stat-link">
                    <span>Manage Parties</span>
                    <i class="bi bi-arrow-right"></i>
                </a>
            </div>
        </div>

        <div class="stat-card stat-card-info" data-aos="fade-up" data-aos-delay="400">
            <div class="stat-card-header">
                <div class="stat-icon">
                    <i class="bi bi-geo-alt"></i>
                </div>
                <div class="stat-trend positive">
                    <i class="bi bi-arrow-up"></i>
                    <span>+3%</span>
                </div>
            </div>
            <div class="stat-content">
                <div class="stat-number"><?php echo e(number_format(\App\Models\Constituency::count() ?? 0)); ?></div>
                <div class="stat-label">Constituencies</div>
                <div class="stat-details">
                    <div class="detail-item">
                        <span class="detail-dot national"></span>
                        <span>National: <?php echo e(\App\Models\Constituency::where('type', 'national')->count() ?? 0); ?></span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-dot provincial"></span>
                        <span>Provincial: <?php echo e(\App\Models\Constituency::where('type', 'provincial')->count() ?? 0); ?></span>
                    </div>
                </div>
            </div>
            <div class="stat-footer">
                <a href="<?php echo e(route('admin.constituencies')); ?>" class="stat-link">
                    <span>Manage Constituencies</span>
                    <i class="bi bi-arrow-right"></i>
                </a>
            </div>
        </div>
    </div>

    <!-- Analytics Dashboard -->
    <div class="analytics-section">
        <div class="analytics-grid">
            <!-- Main Chart -->
            <div class="chart-card main-chart" data-aos="fade-up" data-aos-delay="500">
                <div class="chart-header">
                    <div class="chart-title">
                        <h3>User Registration Trends</h3>
                        <p>Last 30 days performance overview</p>
                    </div>
                    <div class="chart-controls">
                        <div class="time-selector">
                            <button class="time-btn active" data-period="7d">7D</button>
                            <button class="time-btn" data-period="30d">30D</button>
                            <button class="time-btn" data-period="90d">90D</button>
                        </div>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="mainChart"></canvas>
                </div>
                <div class="chart-insights">
                    <div class="insight-item">
                        <div class="insight-icon success">
                            <i class="bi bi-arrow-up"></i>
                        </div>
                        <div class="insight-text">
                            <span class="insight-value">+23%</span>
                            <span class="insight-label">vs last month</span>
                        </div>
                    </div>
                    <div class="insight-item">
                        <div class="insight-icon info">
                            <i class="bi bi-people"></i>
                        </div>
                        <div class="insight-text">
                            <span class="insight-value"><?php echo e(\App\Models\User::whereDate('created_at', today())->count()); ?></span>
                            <span class="insight-label">new today</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Health -->
            <div class="health-card" data-aos="fade-up" data-aos-delay="600">
                <div class="health-header">
                    <h3>System Health</h3>
                    <div class="health-score">
                        <div class="score-circle">
                            <svg class="score-svg" viewBox="0 0 36 36">
                                <path class="score-bg" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"/>
                                <path class="score-fill" stroke-dasharray="95, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"/>
                            </svg>
                            <div class="score-text">95%</div>
                        </div>
                    </div>
                </div>
                <div class="health-metrics">
                    <div class="metric-item">
                        <div class="metric-status online"></div>
                        <div class="metric-info">
                            <div class="metric-name">Database</div>
                            <div class="metric-value">Connected</div>
                        </div>
                        <div class="metric-indicator">
                            <i class="bi bi-check-circle text-success"></i>
                        </div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-status online"></div>
                        <div class="metric-info">
                            <div class="metric-name">Email Service</div>
                            <div class="metric-value">Operational</div>
                        </div>
                        <div class="metric-indicator">
                            <i class="bi bi-check-circle text-success"></i>
                        </div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-status online"></div>
                        <div class="metric-info">
                            <div class="metric-name">SMS Gateway</div>
                            <div class="metric-value">Active</div>
                        </div>
                        <div class="metric-indicator">
                            <i class="bi bi-check-circle text-success"></i>
                        </div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-status warning"></div>
                        <div class="metric-info">
                            <div class="metric-name">ECP Integration</div>
                            <div class="metric-value">Testing Phase</div>
                        </div>
                        <div class="metric-indicator">
                            <i class="bi bi-exclamation-triangle text-warning"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Activity & Quick Actions -->
    <div class="activity-section">
        <div class="activity-grid">
            <!-- Recent Activity -->
            <div class="activity-card" data-aos="fade-up" data-aos-delay="700">
                <div class="activity-header">
                    <h3>Recent Activity</h3>
                    <a href="<?php echo e(route('admin.users')); ?>" class="view-all-btn">
                        <span>View All</span>
                        <i class="bi bi-arrow-right"></i>
                    </a>
                </div>
                <div class="activity-feed">
                    <?php $__currentLoopData = \App\Models\User::latest()->take(5)->get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="activity-item">
                            <div class="activity-avatar">
                                <img src="https://ui-avatars.com/api/?name=<?php echo e(urlencode($user->name)); ?>&background=2d5a27&color=fff"
                                     alt="Avatar" class="avatar-img">
                                <div class="activity-status <?php echo e($user->user_type); ?>"></div>
                            </div>
                            <div class="activity-content">
                                <div class="activity-main">
                                    <span class="activity-user"><?php echo e($user->name); ?></span>
                                    <span class="activity-action">joined as <?php echo e($user->user_type); ?></span>
                                </div>
                                <div class="activity-meta">
                                    <span class="activity-email"><?php echo e($user->email); ?></span>
                                    <span class="activity-time"><?php echo e($user->created_at->diffForHumans()); ?></span>
                                </div>
                            </div>
                            <div class="activity-badge">
                                <span class="role-badge <?php echo e($user->user_type); ?>">
                                    <?php echo e(ucfirst($user->user_type)); ?>

                                </span>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="actions-card" data-aos="fade-up" data-aos-delay="800">
                <div class="actions-header">
                    <h3>Quick Actions</h3>
                    <p>Frequently used administrative tasks</p>
                </div>
                <div class="actions-grid">
                    <a href="<?php echo e(route('admin.users')); ?>" class="action-tile primary">
                        <div class="action-icon">
                            <i class="bi bi-person-plus"></i>
                        </div>
                        <div class="action-content">
                            <h4>Add User</h4>
                            <p>Create new user account</p>
                        </div>
                        <div class="action-arrow">
                            <i class="bi bi-arrow-right"></i>
                        </div>
                    </a>

                    <a href="<?php echo e(route('admin.tenants')); ?>" class="action-tile success">
                        <div class="action-icon">
                            <i class="bi bi-building-add"></i>
                        </div>
                        <div class="action-content">
                            <h4>Organizations</h4>
                            <p>Manage organizations</p>
                        </div>
                        <div class="action-arrow">
                            <i class="bi bi-arrow-right"></i>
                        </div>
                    </a>

                    <a href="<?php echo e(route('admin.parties')); ?>" class="action-tile warning">
                        <div class="action-icon">
                            <i class="bi bi-flag-fill"></i>
                        </div>
                        <div class="action-content">
                            <h4>Political Parties</h4>
                            <p>Manage parties</p>
                        </div>
                        <div class="action-arrow">
                            <i class="bi bi-arrow-right"></i>
                        </div>
                    </a>

                    <a href="<?php echo e(route('admin.settings')); ?>" class="action-tile info">
                        <div class="action-icon">
                            <i class="bi bi-gear-fill"></i>
                        </div>
                        <div class="action-content">
                            <h4>Settings</h4>
                            <p>System configuration</p>
                        </div>
                        <div class="action-arrow">
                            <i class="bi bi-arrow-right"></i>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
<style>
/* Perfect Dashboard Styles */
.perfect-dashboard {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
    overflow-x: hidden;
}

/* Hero Welcome Section */
.hero-welcome {
    position: relative;
    padding: 60px 0;
    margin-bottom: 40px;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.hero-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 2px, transparent 2px),
        radial-gradient(circle at 75% 75%, rgba(255,255,255,0.1) 2px, transparent 2px);
    background-size: 50px 50px;
    animation: float 20s ease-in-out infinite;
}

.hero-gradient {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(45, 90, 39, 0.9) 0%,
        rgba(40, 167, 69, 0.8) 50%,
        rgba(74, 124, 89, 0.9) 100%);
}

.hero-content {
    position: relative;
    z-index: 2;
    color: white;
    padding: 0 30px;
}

.greeting-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    padding: 8px 16px;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.2;
    margin-bottom: 20px;
    text-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

.hero-title .highlight {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.3rem;
    opacity: 0.9;
    margin-bottom: 30px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.hero-stats {
    display: flex;
    gap: 30px;
    margin-top: 30px;
}

.mini-stat {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.mini-stat-value {
    font-size: 2rem;
    font-weight: 700;
    display: block;
    margin-bottom: 5px;
}

.mini-stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Dashboard Clock */
.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.dashboard-clock {
    position: relative;
    width: 200px;
    height: 200px;
}

.clock-face {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    z-index: 3;
}

.clock-time {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.clock-date {
    font-size: 1rem;
    opacity: 0.9;
    margin-bottom: 2px;
}

.clock-day {
    font-size: 0.8rem;
    opacity: 0.7;
}

.clock-rings {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.ring {
    position: absolute;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    animation: rotate 20s linear infinite;
}

.ring-1 {
    top: 10px;
    left: 10px;
    right: 10px;
    bottom: 10px;
    border-top-color: rgba(255, 255, 255, 0.6);
}

.ring-2 {
    top: 20px;
    left: 20px;
    right: 20px;
    bottom: 20px;
    border-right-color: rgba(255, 255, 255, 0.4);
    animation-direction: reverse;
    animation-duration: 15s;
}

.ring-3 {
    top: 30px;
    left: 30px;
    right: 30px;
    bottom: 30px;
    border-bottom-color: rgba(255, 255, 255, 0.3);
    animation-duration: 25s;
}

/* Statistics Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    margin-bottom: 50px;
    padding: 0 30px;
}

.stat-card {
    background: white;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--card-color), var(--card-color-light));
}

.stat-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.stat-card-primary {
    --card-color: #007bff;
    --card-color-light: #66b3ff;
}

.stat-card-success {
    --card-color: #28a745;
    --card-color-light: #71dd8a;
}

.stat-card-warning {
    --card-color: #ffc107;
    --card-color-light: #ffda6a;
}

.stat-card-info {
    --card-color: #17a2b8;
    --card-color-light: #6cc4d0;
}

.stat-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    background: linear-gradient(135deg, var(--card-color), var(--card-color-light));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    box-shadow: 0 8px 16px rgba(0,0,0,0.1);
}

.stat-trend {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.stat-trend.positive {
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--card-color);
    margin-bottom: 5px;
}

.stat-label {
    font-size: 1.1rem;
    color: #6c757d;
    margin-bottom: 15px;
    font-weight: 500;
}

.stat-details {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 20px;
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    color: #6c757d;
}

.detail-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

.detail-dot.active { background: #28a745; }
.detail-dot.inactive { background: #dc3545; }
.detail-dot.pro { background: #ffc107; }
.detail-dot.verified { background: #17a2b8; }
.detail-dot.national { background: #007bff; }
.detail-dot.provincial { background: #6f42c1; }

.stat-footer {
    border-top: 1px solid #f8f9fa;
    padding-top: 15px;
}

.stat-link {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: var(--card-color);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.stat-link:hover {
    color: var(--card-color);
    transform: translateX(5px);
}

/* Analytics Section */
.analytics-section {
    padding: 0 30px;
    margin-bottom: 50px;
}

.analytics-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
}

.chart-card {
    background: white;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 30px;
}

.chart-title h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 5px;
}

.chart-title p {
    color: #6c757d;
    margin: 0;
}

.time-selector {
    display: flex;
    background: #f8f9fa;
    border-radius: 10px;
    padding: 4px;
}

.time-btn {
    padding: 8px 16px;
    border: none;
    background: none;
    border-radius: 8px;
    font-weight: 500;
    color: #6c757d;
    transition: all 0.3s ease;
}

.time-btn.active {
    background: #007bff;
    color: white;
    box-shadow: 0 2px 4px rgba(0,123,255,0.3);
}

.chart-container {
    height: 300px;
    margin-bottom: 20px;
}

.chart-insights {
    display: flex;
    gap: 30px;
    padding-top: 20px;
    border-top: 1px solid #f8f9fa;
}

.insight-item {
    display: flex;
    align-items: center;
    gap: 12px;
}

.insight-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.1rem;
}

.insight-icon.success { background: #28a745; }
.insight-icon.info { background: #17a2b8; }

.insight-value {
    font-size: 1.2rem;
    font-weight: 700;
    color: #2c3e50;
    display: block;
}

.insight-label {
    font-size: 0.9rem;
    color: #6c757d;
}

/* Health Card */
.health-card {
    background: white;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.health-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.health-header h3 {
    font-size: 1.3rem;
    font-weight: 700;
    color: #2c3e50;
}

.health-score {
    position: relative;
}

.score-circle {
    position: relative;
    width: 60px;
    height: 60px;
}

.score-svg {
    width: 100%;
    height: 100%;
    transform: rotate(-90deg);
}

.score-bg {
    fill: none;
    stroke: #f8f9fa;
    stroke-width: 3;
}

.score-fill {
    fill: none;
    stroke: #28a745;
    stroke-width: 3;
    stroke-linecap: round;
    animation: progress 2s ease-in-out;
}

.score-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 0.9rem;
    font-weight: 700;
    color: #28a745;
}

.health-metrics {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.metric-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.metric-item:hover {
    background: #e9ecef;
    transform: translateX(5px);
}

.metric-status {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    flex-shrink: 0;
}

.metric-status.online {
    background: #28a745;
    animation: pulse 2s infinite;
}

.metric-status.warning {
    background: #ffc107;
}

.metric-info {
    flex: 1;
}

.metric-name {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 2px;
}

.metric-value {
    font-size: 0.9rem;
    color: #6c757d;
}

/* Activity Section */
.activity-section {
    padding: 0 30px;
    margin-bottom: 50px;
}

.activity-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

.activity-card, .actions-card {
    background: white;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.activity-header, .actions-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
}

.activity-header h3, .actions-header h3 {
    font-size: 1.3rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0;
}

.actions-header p {
    color: #6c757d;
    margin: 5px 0 0 0;
    font-size: 0.9rem;
}

.view-all-btn {
    display: flex;
    align-items: center;
    gap: 5px;
    color: #007bff;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.view-all-btn:hover {
    color: #0056b3;
    transform: translateX(3px);
}

.activity-feed {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.activity-item:hover {
    background: #e9ecef;
    transform: translateX(5px);
}

.activity-avatar {
    position: relative;
    flex-shrink: 0;
}

.activity-avatar .avatar-img {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    border: 2px solid white;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.activity-status {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid white;
}

.activity-status.admin { background: #dc3545; }
.activity-status.candidate { background: #007bff; }
.activity-status.agent { background: #28a745; }
.activity-status.volunteer { background: #ffc107; }

.activity-content {
    flex: 1;
}

.activity-main {
    margin-bottom: 5px;
}

.activity-user {
    font-weight: 600;
    color: #2c3e50;
}

.activity-action {
    color: #6c757d;
    margin-left: 5px;
}

.activity-meta {
    display: flex;
    gap: 15px;
    font-size: 0.8rem;
    color: #6c757d;
}

.role-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
}

.role-badge.admin { background: rgba(220, 53, 69, 0.1); color: #dc3545; }
.role-badge.candidate { background: rgba(0, 123, 255, 0.1); color: #007bff; }
.role-badge.agent { background: rgba(40, 167, 69, 0.1); color: #28a745; }
.role-badge.volunteer { background: rgba(255, 193, 7, 0.1); color: #ffc107; }

/* Actions Grid */
.actions-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.action-tile {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 15px;
    text-decoration: none;
    color: #2c3e50;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.action-tile::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--action-color);
    transform: scaleY(0);
    transition: transform 0.3s ease;
}

.action-tile:hover::before {
    transform: scaleY(1);
}

.action-tile:hover {
    background: white;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    transform: translateY(-3px);
    color: #2c3e50;
}

.action-tile.primary { --action-color: #007bff; }
.action-tile.success { --action-color: #28a745; }
.action-tile.warning { --action-color: #ffc107; }
.action-tile.info { --action-color: #17a2b8; }

.action-tile .action-icon {
    width: 45px;
    height: 45px;
    border-radius: 12px;
    background: linear-gradient(135deg, var(--action-color), var(--action-color));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    flex-shrink: 0;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.action-content {
    flex: 1;
}

.action-content h4 {
    font-size: 1rem;
    font-weight: 600;
    margin: 0 0 5px 0;
    color: #2c3e50;
}

.action-content p {
    font-size: 0.8rem;
    color: #6c757d;
    margin: 0;
}

.action-arrow {
    color: #6c757d;
    transition: all 0.3s ease;
}

.action-tile:hover .action-arrow {
    color: var(--action-color);
    transform: translateX(3px);
}

/* Animations */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(40, 167, 69, 0); }
    100% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0); }
}

@keyframes progress {
    from { stroke-dasharray: 0 100; }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .analytics-grid {
        grid-template-columns: 1fr;
    }

    .activity-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 15px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        padding: 0 15px;
    }

    .analytics-section,
    .activity-section {
        padding: 0 15px;
    }

    .actions-grid {
        grid-template-columns: 1fr;
    }

    .dashboard-clock {
        width: 150px;
        height: 150px;
    }

    .clock-time {
        font-size: 2rem;
    }
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Initialize AOS (Animate On Scroll)
AOS.init({
    duration: 800,
    easing: 'ease-in-out',
    once: true,
    offset: 100
});

// Perfect Chart Implementation
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('mainChart');
    if (ctx) {
        const chartCtx = ctx.getContext('2d');

        // Create gradient
        const gradient = chartCtx.createLinearGradient(0, 0, 0, 300);
        gradient.addColorStop(0, 'rgba(0, 123, 255, 0.3)');
        gradient.addColorStop(1, 'rgba(0, 123, 255, 0.05)');

        new Chart(chartCtx, {
            type: 'line',
            data: {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],
                datasets: [{
                    label: 'New Users',
                    data: [65, 78, 90, 81, 95, 105, 120],
                    borderColor: '#007bff',
                    backgroundColor: gradient,
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: '#007bff',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 3,
                    pointRadius: 6,
                    pointHoverRadius: 8,
                    pointHoverBackgroundColor: '#007bff',
                    pointHoverBorderColor: '#ffffff',
                    pointHoverBorderWidth: 3
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#ffffff',
                        bodyColor: '#ffffff',
                        borderColor: '#007bff',
                        borderWidth: 1,
                        cornerRadius: 8,
                        displayColors: false,
                        callbacks: {
                            title: function(context) {
                                return 'Month: ' + context[0].label;
                            },
                            label: function(context) {
                                return 'New Users: ' + context.parsed.y;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        },
                        border: {
                            display: false
                        },
                        ticks: {
                            color: '#6c757d',
                            font: {
                                size: 12,
                                weight: '500'
                            }
                        }
                    },
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)',
                            drawBorder: false
                        },
                        border: {
                            display: false
                        },
                        ticks: {
                            color: '#6c757d',
                            font: {
                                size: 12,
                                weight: '500'
                            },
                            callback: function(value) {
                                return value + ' users';
                            }
                        }
                    }
                },
                elements: {
                    point: {
                        hoverRadius: 8
                    }
                },
                animation: {
                    duration: 2000,
                    easing: 'easeInOutQuart'
                }
            }
        });
    }

    // Time selector functionality
    const timeButtons = document.querySelectorAll('.time-btn');
    timeButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            timeButtons.forEach(b => b.classList.remove('active'));
            this.classList.add('active');

            // Here you would typically update the chart data
            console.log('Selected period:', this.dataset.period);
        });
    });

    // Add smooth scrolling for internal links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Add loading states for action tiles
    document.querySelectorAll('.action-tile').forEach(tile => {
        tile.addEventListener('click', function(e) {
            if (!this.href.includes('#')) {
                const icon = this.querySelector('.action-icon i');
                const originalClass = icon.className;
                icon.className = 'bi bi-arrow-clockwise';
                icon.style.animation = 'rotate 1s linear infinite';

                setTimeout(() => {
                    icon.className = originalClass;
                    icon.style.animation = '';
                }, 1000);
            }
        });
    });
});

// Real-time clock update
function updateClock() {
    const now = new Date();
    const timeElement = document.querySelector('.clock-time');
    const dateElement = document.querySelector('.clock-date');
    const dayElement = document.querySelector('.clock-day');

    if (timeElement) {
        timeElement.textContent = now.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: false
        });
    }

    if (dateElement) {
        dateElement.textContent = now.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric'
        });
    }

    if (dayElement) {
        dayElement.textContent = now.toLocaleDateString('en-US', {
            weekday: 'long'
        });
    }
}

// Update clock every second
setInterval(updateClock, 1000);
updateClock(); // Initial call
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.modern', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\ECP\pakvote\resources\views/admin/modern-dashboard.blade.php ENDPATH**/ ?>