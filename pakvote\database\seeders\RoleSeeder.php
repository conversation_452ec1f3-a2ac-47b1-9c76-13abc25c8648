<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create roles
        $roles = [
            'super_admin',
            'admin',
            'candidate',
            'agent',
            'volunteer'
        ];

        foreach ($roles as $role) {
            Role::firstOrCreate(['name' => $role]);
        }

        // Create permissions
        $permissions = [
            // User management
            'manage_users',
            'view_users',
            'create_users',
            'edit_users',
            'delete_users',

            // Tenant management
            'manage_tenants',
            'view_tenants',
            'create_tenants',
            'edit_tenants',
            'delete_tenants',

            // Candidate management
            'manage_candidates',
            'view_candidates',
            'create_candidates',
            'edit_candidates',
            'delete_candidates',

            // Campaign management
            'manage_campaigns',
            'view_campaigns',
            'create_campaigns',
            'edit_campaigns',
            'delete_campaigns',

            // Voter management
            'manage_voters',
            'view_voters',
            'create_voters',
            'edit_voters',
            'delete_voters',

            // Party management
            'manage_parties',
            'view_parties',
            'create_parties',
            'edit_parties',
            'delete_parties',

            // Constituency management
            'manage_constituencies',
            'view_constituencies',
            'create_constituencies',
            'edit_constituencies',
            'delete_constituencies',

            // Reports and analytics
            'view_reports',
            'export_data',

            // System settings
            'manage_settings',
            'view_settings',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Assign permissions to roles
        $superAdmin = Role::findByName('super_admin');
        $superAdmin->givePermissionTo(Permission::all());

        $admin = Role::findByName('admin');
        $admin->givePermissionTo([
            'manage_users', 'view_users', 'create_users', 'edit_users',
            'manage_candidates', 'view_candidates', 'create_candidates', 'edit_candidates',
            'manage_campaigns', 'view_campaigns', 'create_campaigns', 'edit_campaigns',
            'manage_voters', 'view_voters', 'create_voters', 'edit_voters',
            'manage_parties', 'view_parties', 'create_parties', 'edit_parties',
            'manage_constituencies', 'view_constituencies', 'create_constituencies', 'edit_constituencies',
            'view_reports', 'export_data',
            'manage_settings', 'view_settings',
        ]);

        $candidate = Role::findByName('candidate');
        $candidate->givePermissionTo([
            'view_campaigns', 'create_campaigns', 'edit_campaigns',
            'view_voters', 'create_voters', 'edit_voters',
            'view_reports',
        ]);

        $agent = Role::findByName('agent');
        $agent->givePermissionTo([
            'view_campaigns',
            'view_voters', 'create_voters', 'edit_voters',
        ]);

        $volunteer = Role::findByName('volunteer');
        $volunteer->givePermissionTo([
            'view_campaigns',
            'view_voters',
        ]);
    }
}
