<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tenants', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->json('name'); // Translatable field
            $table->string('domain')->unique();
            $table->string('database')->nullable();
            $table->enum('subscription_plan', ['free', 'pro', 'enterprise'])->default('free');
            $table->enum('subscription_status', ['active', 'inactive', 'suspended', 'cancelled'])->default('active');
            $table->timestamp('subscription_ends_at')->nullable();
            $table->json('settings')->nullable();
            $table->boolean('is_active')->default(true);
            $table->string('contact_email');
            $table->string('contact_phone')->nullable();
            $table->text('address')->nullable();
            $table->string('logo')->nullable();
            $table->timestamps();

            $table->index(['subscription_plan', 'subscription_status']);
            $table->index('is_active');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tenants');
    }
};
