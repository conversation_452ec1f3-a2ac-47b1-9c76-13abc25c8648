<?php $__env->startSection('title', 'Manage Organizations'); ?>

<?php $__env->startSection('sidebar'); ?>
<div class="list-group list-group-flush">
    <a href="<?php echo e(route('admin.dashboard')); ?>" class="list-group-item list-group-item-action">
        <i class="bi bi-speedometer2"></i> Dashboard
    </a>
    <a href="<?php echo e(route('admin.users')); ?>" class="list-group-item list-group-item-action">
        <i class="bi bi-people"></i> Users
    </a>
    <a href="<?php echo e(route('admin.tenants')); ?>" class="list-group-item list-group-item-action active">
        <i class="bi bi-building"></i> Organizations
    </a>
    <a href="<?php echo e(route('admin.parties')); ?>" class="list-group-item list-group-item-action">
        <i class="bi bi-flag"></i> Political Parties
    </a>
    <a href="<?php echo e(route('admin.constituencies')); ?>" class="list-group-item list-group-item-action">
        <i class="bi bi-geo-alt"></i> Constituencies
    </a>
    <a href="<?php echo e(route('admin.settings')); ?>" class="list-group-item list-group-item-action">
        <i class="bi bi-gear"></i> Settings
    </a>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="perfect-tenants-page">
    <!-- Page Header -->
    <div class="page-header">
        <div class="header-content">
            <div class="header-main">
                <div class="header-icon">
                    <i class="bi bi-building"></i>
                </div>
                <div class="header-text">
                    <h1>Organizations Management</h1>
                    <p>Manage and monitor all registered organizations in the system</p>
                </div>
            </div>
            <div class="header-actions">
                <button class="btn btn-primary btn-modern" data-bs-toggle="modal" data-bs-target="#addTenantModal">
                    <i class="bi bi-plus-circle"></i>
                    <span>Add Organization</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Perfect Statistics Cards -->
    <div class="stats-overview">
        <div class="stat-card-modern primary" data-aos="fade-up" data-aos-delay="100">
            <div class="stat-icon-wrapper">
                <div class="stat-icon">
                    <i class="bi bi-building"></i>
                </div>
                <div class="stat-pulse"></div>
            </div>
            <div class="stat-content">
                <div class="stat-number"><?php echo e(number_format($stats['total'] ?? 0)); ?></div>
                <div class="stat-label">Total Organizations</div>
                <div class="stat-trend positive">
                    <i class="bi bi-arrow-up"></i>
                    <span>+12% from last month</span>
                </div>
            </div>
        </div>

        <div class="stat-card-modern success" data-aos="fade-up" data-aos-delay="200">
            <div class="stat-icon-wrapper">
                <div class="stat-icon">
                    <i class="bi bi-check-circle"></i>
                </div>
                <div class="stat-pulse"></div>
            </div>
            <div class="stat-content">
                <div class="stat-number"><?php echo e(number_format($stats['active'] ?? 0)); ?></div>
                <div class="stat-label">Active Organizations</div>
                <div class="stat-trend positive">
                    <i class="bi bi-arrow-up"></i>
                    <span>+8% from last month</span>
                </div>
            </div>
        </div>

        <div class="stat-card-modern warning" data-aos="fade-up" data-aos-delay="300">
            <div class="stat-icon-wrapper">
                <div class="stat-icon">
                    <i class="bi bi-star"></i>
                </div>
                <div class="stat-pulse"></div>
            </div>
            <div class="stat-content">
                <div class="stat-number"><?php echo e(number_format($stats['pro'] ?? 0)); ?></div>
                <div class="stat-label">Pro Plans</div>
                <div class="stat-trend positive">
                    <i class="bi bi-arrow-up"></i>
                    <span>+15% from last month</span>
                </div>
            </div>
        </div>

        <div class="stat-card-modern info" data-aos="fade-up" data-aos-delay="400">
            <div class="stat-icon-wrapper">
                <div class="stat-icon">
                    <i class="bi bi-gem"></i>
                </div>
                <div class="stat-pulse"></div>
            </div>
            <div class="stat-content">
                <div class="stat-number"><?php echo e(number_format($stats['enterprise'] ?? 0)); ?></div>
                <div class="stat-label">Enterprise Plans</div>
                <div class="stat-trend positive">
                    <i class="bi bi-arrow-up"></i>
                    <span>+5% from last month</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Modern Organizations Table -->
    <div class="organizations-section" data-aos="fade-up" data-aos-delay="500">
        <div class="section-header">
            <div class="section-title">
                <h2>Organizations Directory</h2>
                <p>Complete list of all registered organizations</p>
            </div>
            <div class="section-filters">
                <div class="search-box">
                    <i class="bi bi-search"></i>
                    <input type="text" placeholder="Search organizations..." class="search-input">
                </div>
                <div class="filter-dropdown">
                    <select class="form-select">
                        <option>All Status</option>
                        <option>Active</option>
                        <option>Inactive</option>
                    </select>
                </div>
            </div>
        </div>

        <?php if($tenants->count() > 0): ?>
            <div class="organizations-grid">
                <?php $__currentLoopData = $tenants; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tenant): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="organization-card" data-aos="fade-up" data-aos-delay="<?php echo e(600 + $loop->index * 100); ?>">
                        <div class="org-header">
                            <div class="org-avatar">
                                <?php if($tenant->logo): ?>
                                    <img src="<?php echo e($tenant->logo); ?>" alt="Logo" class="org-logo">
                                <?php else: ?>
                                    <div class="org-placeholder">
                                        <?php echo e(strtoupper(substr($tenant->name['en'] ?? $tenant->name, 0, 2))); ?>

                                    </div>
                                <?php endif; ?>
                                <div class="org-status <?php echo e($tenant->is_active ? 'active' : 'inactive'); ?>"></div>
                            </div>
                            <div class="org-info">
                                <h3 class="org-name"><?php echo e($tenant->name['en'] ?? $tenant->name); ?></h3>
                                <p class="org-domain"><?php echo e($tenant->domain); ?></p>
                                <div class="org-id">ID: <?php echo e(substr($tenant->id, 0, 8)); ?>...</div>
                            </div>
                            <div class="org-actions">
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                        <i class="bi bi-three-dots"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="<?php echo e(route('admin.tenants.edit', $tenant)); ?>">
                                            <i class="bi bi-pencil"></i> Edit
                                        </a></li>
                                        <li><a class="dropdown-item" href="#">
                                            <i class="bi bi-people"></i> View Users
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <form method="POST" action="<?php echo e(route('admin.tenants.delete', $tenant)); ?>" class="d-inline" onsubmit="return confirm('Are you sure?')">
                                                <?php echo csrf_field(); ?>
                                                <?php echo method_field('DELETE'); ?>
                                                <button type="submit" class="dropdown-item text-danger">
                                                    <i class="bi bi-trash"></i> Delete
                                                </button>
                                            </form>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="org-details">
                            <div class="detail-row">
                                <div class="detail-item">
                                    <i class="bi bi-envelope"></i>
                                    <span><?php echo e($tenant->contact_email); ?></span>
                                </div>
                                <?php if($tenant->contact_phone): ?>
                                    <div class="detail-item">
                                        <i class="bi bi-telephone"></i>
                                        <span><?php echo e($tenant->contact_phone); ?></span>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="org-footer">
                            <div class="org-metrics">
                                <div class="metric">
                                    <span class="metric-value"><?php echo e($tenant->users_count ?? 0); ?></span>
                                    <span class="metric-label">Users</span>
                                </div>
                                <div class="metric">
                                    <span class="metric-value"><?php echo e($tenant->created_at->format('M Y')); ?></span>
                                    <span class="metric-label">Joined</span>
                                </div>
                            </div>
                            <div class="org-badges">
                                <span class="plan-badge <?php echo e($tenant->subscription_plan); ?>">
                                    <?php echo e(ucfirst($tenant->subscription_plan)); ?>

                                </span>
                                <span class="status-badge <?php echo e($tenant->subscription_status); ?>">
                                    <?php echo e(ucfirst($tenant->subscription_status)); ?>

                                </span>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <!-- Pagination -->
            <div class="pagination-wrapper">
                <?php echo e($tenants->links()); ?>

            </div>
        <?php else: ?>
            <div class="empty-state">
                <div class="empty-illustration">
                    <i class="bi bi-building"></i>
                </div>
                <h3>No Organizations Found</h3>
                <p>Get started by adding your first organization to the system.</p>
                <button class="btn btn-primary btn-modern" data-bs-toggle="modal" data-bs-target="#addTenantModal">
                    <i class="bi bi-plus-circle"></i>
                    <span>Add First Organization</span>
                </button>
            </div>
        <?php endif; ?>
    </div>

<!-- Add Tenant Modal -->
<div class="modal fade" id="addTenantModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Organization</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="<?php echo e(route('admin.tenants.store')); ?>">
                <?php echo csrf_field(); ?>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="name" class="form-label">Organization Name</label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="domain" class="form-label">Domain</label>
                        <input type="text" class="form-control" id="domain" name="domain" required>
                    </div>
                    <div class="mb-3">
                        <label for="contact_email" class="form-label">Contact Email</label>
                        <input type="email" class="form-control" id="contact_email" name="contact_email" required>
                    </div>
                    <div class="mb-3">
                        <label for="contact_phone" class="form-label">Contact Phone</label>
                        <input type="tel" class="form-control" id="contact_phone" name="contact_phone">
                    </div>
                    <div class="mb-3">
                        <label for="subscription_plan" class="form-label">Subscription Plan</label>
                        <select class="form-select" id="subscription_plan" name="subscription_plan">
                            <option value="free">Free</option>
                            <option value="pro">Pro</option>
                            <option value="enterprise">Enterprise</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Organization</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
<style>
/* Perfect Tenants Page Styles */
.perfect-tenants-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    padding: 30px;
}

/* Page Header */
.page-header {
    background: white;
    border-radius: 20px;
    padding: 40px;
    margin-bottom: 40px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #007bff, #28a745, #ffc107, #17a2b8);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-main {
    display: flex;
    align-items: center;
    gap: 20px;
}

.header-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    box-shadow: 0 8px 16px rgba(0,123,255,0.3);
}

.header-text h1 {
    font-size: 2.5rem;
    font-weight: 800;
    color: #2c3e50;
    margin: 0 0 10px 0;
}

.header-text p {
    color: #6c757d;
    font-size: 1.1rem;
    margin: 0;
}

.btn-modern {
    padding: 12px 24px;
    border-radius: 12px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 12px rgba(0,123,255,0.3);
}

.btn-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0,123,255,0.4);
}

/* Statistics Overview */
.stats-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    margin-bottom: 50px;
}

.stat-card-modern {
    background: white;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.stat-card-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--card-color);
}

.stat-card-modern:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.stat-card-modern.primary { --card-color: #007bff; }
.stat-card-modern.success { --card-color: #28a745; }
.stat-card-modern.warning { --card-color: #ffc107; }
.stat-card-modern.info { --card-color: #17a2b8; }

.stat-icon-wrapper {
    position: relative;
    margin-bottom: 20px;
}

.stat-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, var(--card-color), var(--card-color));
    border-radius: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.8rem;
    box-shadow: 0 8px 16px rgba(0,0,0,0.1);
    position: relative;
    z-index: 2;
}

.stat-pulse {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 70px;
    height: 70px;
    border-radius: 18px;
    background: var(--card-color);
    opacity: 0.3;
    animation: pulse-stat 2s infinite;
}

.stat-number {
    font-size: 3rem;
    font-weight: 800;
    color: var(--card-color);
    margin-bottom: 8px;
    line-height: 1;
}

.stat-label {
    font-size: 1.1rem;
    color: #6c757d;
    margin-bottom: 15px;
    font-weight: 500;
}

.stat-trend {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.9rem;
    font-weight: 600;
    padding: 6px 12px;
    border-radius: 20px;
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
    width: fit-content;
}

/* Organizations Section */
.organizations-section {
    background: white;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 40px;
}

.section-title h2 {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0 0 8px 0;
}

.section-title p {
    color: #6c757d;
    margin: 0;
}

.section-filters {
    display: flex;
    gap: 15px;
    align-items: center;
}

.search-box {
    position: relative;
}

.search-box i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
}

.search-input {
    padding: 12px 15px 12px 45px;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    font-size: 0.9rem;
    width: 250px;
    transition: all 0.3s ease;
}

.search-input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
}

/* Organizations Grid */
.organizations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.organization-card {
    background: #f8f9fa;
    border-radius: 16px;
    padding: 25px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid transparent;
}

.organization-card:hover {
    background: white;
    border-color: #007bff;
    box-shadow: 0 12px 30px rgba(0,123,255,0.15);
    transform: translateY(-5px);
}

.org-header {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    margin-bottom: 20px;
}

.org-avatar {
    position: relative;
    flex-shrink: 0;
}

.org-logo {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    object-fit: cover;
}

.org-placeholder {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 1.2rem;
}

.org-status {
    position: absolute;
    bottom: -2px;
    right: -2px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 3px solid white;
}

.org-status.active { background: #28a745; }
.org-status.inactive { background: #dc3545; }

.org-info {
    flex: 1;
}

.org-name {
    font-size: 1.3rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0 0 5px 0;
}

.org-domain {
    color: #007bff;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    margin: 0 0 5px 0;
}

.org-id {
    font-size: 0.8rem;
    color: #6c757d;
}

.org-details {
    margin-bottom: 20px;
}

.detail-row {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    color: #6c757d;
}

.detail-item i {
    width: 16px;
    color: #007bff;
}

.org-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

.org-metrics {
    display: flex;
    gap: 20px;
}

.metric {
    text-align: center;
}

.metric-value {
    display: block;
    font-size: 1.1rem;
    font-weight: 700;
    color: #2c3e50;
}

.metric-label {
    font-size: 0.8rem;
    color: #6c757d;
}

.org-badges {
    display: flex;
    gap: 8px;
}

.plan-badge, .status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
}

.plan-badge.free { background: rgba(108, 117, 125, 0.1); color: #6c757d; }
.plan-badge.pro { background: rgba(255, 193, 7, 0.1); color: #ffc107; }
.plan-badge.enterprise { background: rgba(40, 167, 69, 0.1); color: #28a745; }

.status-badge.active { background: rgba(40, 167, 69, 0.1); color: #28a745; }
.status-badge.inactive { background: rgba(220, 53, 69, 0.1); color: #dc3545; }

/* Empty State */
.empty-state {
    text-align: center;
    padding: 80px 40px;
}

.empty-illustration {
    width: 120px;
    height: 120px;
    background: linear-gradient(135deg, #e9ecef, #dee2e6);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 30px;
    font-size: 3rem;
    color: #6c757d;
}

.empty-state h3 {
    font-size: 1.8rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 15px;
}

.empty-state p {
    color: #6c757d;
    font-size: 1.1rem;
    margin-bottom: 30px;
}

/* Animations */
@keyframes pulse-stat {
    0% { transform: translate(-50%, -50%) scale(1); opacity: 0.3; }
    50% { transform: translate(-50%, -50%) scale(1.1); opacity: 0.1; }
    100% { transform: translate(-50%, -50%) scale(1); opacity: 0.3; }
}

/* Responsive Design */
@media (max-width: 768px) {
    .perfect-tenants-page {
        padding: 15px;
    }

    .header-content {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }

    .header-main {
        flex-direction: column;
        gap: 15px;
    }

    .section-header {
        flex-direction: column;
        gap: 20px;
    }

    .section-filters {
        width: 100%;
        justify-content: center;
    }

    .search-input {
        width: 200px;
    }

    .organizations-grid {
        grid-template-columns: 1fr;
    }

    .stats-overview {
        grid-template-columns: 1fr;
    }
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
<script>
// Initialize AOS (Animate On Scroll)
AOS.init({
    duration: 800,
    easing: 'ease-in-out',
    once: true,
    offset: 100
});

// Search functionality
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.querySelector('.search-input');
    const organizationCards = document.querySelectorAll('.organization-card');

    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();

            organizationCards.forEach(card => {
                const orgName = card.querySelector('.org-name').textContent.toLowerCase();
                const orgDomain = card.querySelector('.org-domain').textContent.toLowerCase();

                if (orgName.includes(searchTerm) || orgDomain.includes(searchTerm)) {
                    card.style.display = 'block';
                    card.style.animation = 'fadeIn 0.3s ease-in-out';
                } else {
                    card.style.display = 'none';
                }
            });
        });
    }

    // Add loading states for action buttons
    document.querySelectorAll('.btn-modern').forEach(btn => {
        btn.addEventListener('click', function() {
            const icon = this.querySelector('i');
            const originalClass = icon.className;

            icon.className = 'bi bi-arrow-clockwise';
            icon.style.animation = 'rotate 1s linear infinite';

            setTimeout(() => {
                icon.className = originalClass;
                icon.style.animation = '';
            }, 1000);
        });
    });

    // Smooth hover effects for organization cards
    organizationCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
});

// Add CSS animation for search results
const style = document.createElement('style');
style.textContent = `
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    @keyframes rotate {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }
`;
document.head.appendChild(style);
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.modern', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\ECP\pakvote\resources\views/admin/tenants.blade.php ENDPATH**/ ?>