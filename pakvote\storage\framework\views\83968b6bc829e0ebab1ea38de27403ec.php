<?php $__env->startSection('title', 'Manage Organizations'); ?>

<?php $__env->startSection('sidebar'); ?>
<div class="list-group list-group-flush">
    <a href="<?php echo e(route('admin.dashboard')); ?>" class="list-group-item list-group-item-action">
        <i class="bi bi-speedometer2"></i> Dashboard
    </a>
    <a href="<?php echo e(route('admin.users')); ?>" class="list-group-item list-group-item-action">
        <i class="bi bi-people"></i> Users
    </a>
    <a href="<?php echo e(route('admin.tenants')); ?>" class="list-group-item list-group-item-action active">
        <i class="bi bi-building"></i> Organizations
    </a>
    <a href="<?php echo e(route('admin.parties')); ?>" class="list-group-item list-group-item-action">
        <i class="bi bi-flag"></i> Political Parties
    </a>
    <a href="<?php echo e(route('admin.constituencies')); ?>" class="list-group-item list-group-item-action">
        <i class="bi bi-geo-alt"></i> Constituencies
    </a>
    <a href="<?php echo e(route('admin.settings')); ?>" class="list-group-item list-group-item-action">
        <i class="bi bi-gear"></i> Settings
    </a>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="bi bi-building text-info"></i> Manage Organizations
    </h1>
    <div>
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addTenantModal">
            <i class="bi bi-plus"></i> Add Organization
        </button>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card border-primary">
            <div class="card-body text-center">
                <i class="bi bi-building display-6 text-primary"></i>
                <h4 class="mt-2"><?php echo e($stats['total'] ?? 0); ?></h4>
                <p class="text-muted mb-0">Total Organizations</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-success">
            <div class="card-body text-center">
                <i class="bi bi-check-circle display-6 text-success"></i>
                <h4 class="mt-2"><?php echo e($stats['active'] ?? 0); ?></h4>
                <p class="text-muted mb-0">Active</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-warning">
            <div class="card-body text-center">
                <i class="bi bi-star display-6 text-warning"></i>
                <h4 class="mt-2"><?php echo e($stats['pro'] ?? 0); ?></h4>
                <p class="text-muted mb-0">Pro Plans</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-info">
            <div class="card-body text-center">
                <i class="bi bi-gem display-6 text-info"></i>
                <h4 class="mt-2"><?php echo e($stats['enterprise'] ?? 0); ?></h4>
                <p class="text-muted mb-0">Enterprise</p>
            </div>
        </div>
    </div>
</div>

<!-- Organizations Table -->
<div class="card">
    <div class="card-header bg-light">
        <h5 class="mb-0">
            <i class="bi bi-list"></i> Organizations List
        </h5>
    </div>
    <div class="card-body">
        <?php if($tenants->count() > 0): ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>Organization</th>
                            <th>Domain</th>
                            <th>Contact</th>
                            <th>Plan</th>
                            <th>Status</th>
                            <th>Users</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $tenants; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tenant): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <?php if($tenant->logo): ?>
                                            <img src="<?php echo e($tenant->logo); ?>" alt="Logo" class="rounded me-2" width="32" height="32">
                                        <?php else: ?>
                                            <div class="bg-primary text-white rounded d-flex align-items-center justify-content-center me-2" style="width: 32px; height: 32px;">
                                                <?php echo e(strtoupper(substr($tenant->name['en'] ?? $tenant->name, 0, 1))); ?>

                                            </div>
                                        <?php endif; ?>
                                        <div>
                                            <h6 class="mb-0"><?php echo e($tenant->name['en'] ?? $tenant->name); ?></h6>
                                            <small class="text-muted"><?php echo e(substr($tenant->id, 0, 8)); ?>...</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <code><?php echo e($tenant->domain); ?></code>
                                </td>
                                <td>
                                    <div>
                                        <small class="d-block"><?php echo e($tenant->contact_email); ?></small>
                                        <?php if($tenant->contact_phone): ?>
                                            <small class="text-muted"><?php echo e($tenant->contact_phone); ?></small>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo e($tenant->subscription_plan === 'enterprise' ? 'success' : ($tenant->subscription_plan === 'pro' ? 'warning' : 'secondary')); ?>">
                                        <?php echo e(ucfirst($tenant->subscription_plan)); ?>

                                    </span>
                                </td>
                                <td>
                                    <div>
                                        <span class="badge bg-<?php echo e($tenant->is_active ? 'success' : 'secondary'); ?>">
                                            <?php echo e($tenant->is_active ? 'Active' : 'Inactive'); ?>

                                        </span>
                                        <br>
                                        <span class="badge bg-<?php echo e($tenant->subscription_status === 'active' ? 'success' : 'danger'); ?> mt-1">
                                            <?php echo e(ucfirst($tenant->subscription_status)); ?>

                                        </span>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-info"><?php echo e($tenant->users_count ?? 0); ?></span>
                                </td>
                                <td>
                                    <small><?php echo e($tenant->created_at->format('M j, Y')); ?></small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="<?php echo e(route('admin.tenants.edit', $tenant)); ?>" class="btn btn-outline-primary" title="Edit">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <button type="button" class="btn btn-outline-info" title="View Users">
                                            <i class="bi bi-people"></i>
                                        </button>
                                        <form method="POST" action="<?php echo e(route('admin.tenants.delete', $tenant)); ?>" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this organization?')">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('DELETE'); ?>
                                            <button type="submit" class="btn btn-outline-danger" title="Delete">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-4">
                <?php echo e($tenants->links()); ?>

            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <i class="bi bi-building display-1 text-muted"></i>
                <h4 class="text-muted mt-3">No Organizations Found</h4>
                <p class="text-muted">Start by adding your first organization.</p>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addTenantModal">
                    <i class="bi bi-plus"></i> Add Organization
                </button>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Add Tenant Modal -->
<div class="modal fade" id="addTenantModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Organization</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="<?php echo e(route('admin.tenants.store')); ?>">
                <?php echo csrf_field(); ?>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="name" class="form-label">Organization Name</label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="domain" class="form-label">Domain</label>
                        <input type="text" class="form-control" id="domain" name="domain" required>
                    </div>
                    <div class="mb-3">
                        <label for="contact_email" class="form-label">Contact Email</label>
                        <input type="email" class="form-control" id="contact_email" name="contact_email" required>
                    </div>
                    <div class="mb-3">
                        <label for="contact_phone" class="form-label">Contact Phone</label>
                        <input type="tel" class="form-control" id="contact_phone" name="contact_phone">
                    </div>
                    <div class="mb-3">
                        <label for="subscription_plan" class="form-label">Subscription Plan</label>
                        <select class="form-select" id="subscription_plan" name="subscription_plan">
                            <option value="free">Free</option>
                            <option value="pro">Pro</option>
                            <option value="enterprise">Enterprise</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Organization</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .table th {
        border-top: none;
        font-weight: 600;
    }
    .card {
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    /* Statistics Cards Styles */
    .modern-card .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        margin: 0 auto 12px;
    }

    .modern-card .stat-value {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 4px;
    }

    .modern-card .stat-label {
        font-size: 0.9rem;
        color: var(--text-secondary);
    }

    .modern-card.hover-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 12px 24px rgba(0,0,0,0.2);
    }
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.modern', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\ECP\pakvote\resources\views/admin/tenants.blade.php ENDPATH**/ ?>