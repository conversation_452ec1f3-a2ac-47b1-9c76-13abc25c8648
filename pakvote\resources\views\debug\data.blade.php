@extends('layouts.app')

@section('title', 'Debug Data Viewer')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">
            <i class="bi bi-database text-info"></i> Debug Data Viewer
        </h1>
        <div class="d-flex gap-2 flex-wrap">
            <a href="{{ route('login') }}" class="btn btn-success">
                <i class="bi bi-box-arrow-in-right"></i> Login Form
            </a>
            <a href="{{ route('debug.login') }}" class="btn btn-warning">
                <i class="bi bi-lightning"></i> Auto Login
            </a>
            <a href="{{ route('working.admin') }}" class="btn btn-info">
                <i class="bi bi-speedometer2"></i> Working Admin
            </a>
            <a href="{{ route('simple.dashboard') }}" class="btn btn-secondary">
                <i class="bi bi-house"></i> Simple Dashboard
            </a>
            <a href="{{ route('debug.candidate') }}" class="btn btn-primary">
                <i class="bi bi-person-badge"></i> Candidate Dashboard
            </a>
        </div>
    </div>

    <!-- Users Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-people"></i> Users ({{ $users->count() }})
                    </h5>
                </div>
                <div class="card-body">
                    @if($users->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>User Type</th>
                                        <th>Tenant</th>
                                        <th>Active</th>
                                        <th>Created</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($users as $user)
                                        <tr>
                                            <td><small>{{ substr($user->id, 0, 8) }}...</small></td>
                                            <td>{{ $user->name }}</td>
                                            <td>{{ $user->email }}</td>
                                            <td>
                                                <span class="badge bg-{{ $user->user_type === 'admin' ? 'danger' : 'primary' }}">
                                                    {{ ucfirst($user->user_type) }}
                                                </span>
                                            </td>
                                            <td>
                                                @if($user->tenant)
                                                    {{ $user->tenant->name['en'] ?? $user->tenant->name }}
                                                @else
                                                    <span class="text-muted">No Tenant</span>
                                                @endif
                                            </td>
                                            <td>
                                                <span class="badge bg-{{ $user->is_active ? 'success' : 'secondary' }}">
                                                    {{ $user->is_active ? 'Active' : 'Inactive' }}
                                                </span>
                                            </td>
                                            <td>{{ $user->created_at->format('M j, Y') }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <p class="text-muted text-center">No users found.</p>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Tenants Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-building"></i> Organizations/Tenants ({{ $tenants->count() }})
                    </h5>
                </div>
                <div class="card-body">
                    @if($tenants->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Name</th>
                                        <th>Domain</th>
                                        <th>Plan</th>
                                        <th>Status</th>
                                        <th>Active</th>
                                        <th>Created</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($tenants as $tenant)
                                        <tr>
                                            <td><small>{{ substr($tenant->id, 0, 8) }}...</small></td>
                                            <td>{{ $tenant->name['en'] ?? $tenant->name }}</td>
                                            <td>{{ $tenant->domain }}</td>
                                            <td>
                                                <span class="badge bg-{{ $tenant->subscription_plan === 'enterprise' ? 'success' : ($tenant->subscription_plan === 'pro' ? 'warning' : 'secondary') }}">
                                                    {{ ucfirst($tenant->subscription_plan) }}
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-{{ $tenant->subscription_status === 'active' ? 'success' : 'danger' }}">
                                                    {{ ucfirst($tenant->subscription_status) }}
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-{{ $tenant->is_active ? 'success' : 'secondary' }}">
                                                    {{ $tenant->is_active ? 'Active' : 'Inactive' }}
                                                </span>
                                            </td>
                                            <td>{{ $tenant->created_at->format('M j, Y') }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <p class="text-muted text-center">No tenants found.</p>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Political Parties Section -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-flag"></i> Political Parties ({{ $parties->count() }})
                    </h5>
                </div>
                <div class="card-body">
                    @if($parties->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Symbol</th>
                                        <th>Short Name</th>
                                        <th>Active</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($parties as $party)
                                        <tr>
                                            <td>{{ $party->name['en'] ?? $party->name }}</td>
                                            <td>{{ $party->symbol }}</td>
                                            <td>{{ $party->short_name }}</td>
                                            <td>
                                                <span class="badge bg-{{ $party->is_active ? 'success' : 'secondary' }}">
                                                    {{ $party->is_active ? 'Active' : 'Inactive' }}
                                                </span>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <p class="text-muted text-center">No political parties found.</p>
                    @endif
                </div>
            </div>
        </div>

        <!-- Constituencies Section -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="bi bi-geo-alt"></i> Constituencies ({{ $constituencies->count() }})
                    </h5>
                </div>
                <div class="card-body">
                    @if($constituencies->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Code</th>
                                        <th>Name</th>
                                        <th>Type</th>
                                        <th>Province</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($constituencies as $constituency)
                                        <tr>
                                            <td>{{ $constituency->code }}</td>
                                            <td>{{ $constituency->name['en'] ?? $constituency->name }}</td>
                                            <td>
                                                <span class="badge bg-primary">
                                                    {{ ucfirst($constituency->type) }}
                                                </span>
                                            </td>
                                            <td>{{ ucfirst($constituency->province) }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <p class="text-muted text-center">No constituencies found.</p>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Login Instructions -->
    <div class="row">
        <div class="col-12">
            <div class="alert alert-info">
                <h4 class="alert-heading">
                    <i class="bi bi-info-circle"></i> Testing Options
                </h4>
                <div class="row">
                    <div class="col-md-6">
                        <h6>Manual Login:</h6>
                        <ul>
                            <li><strong>Email:</strong> <EMAIL></li>
                            <li><strong>Password:</strong> admin123</li>
                        </ul>
                        <a href="{{ route('login') }}" class="btn btn-primary btn-sm">
                            <i class="bi bi-arrow-right"></i> Go to Login Page
                        </a>
                    </div>
                    <div class="col-md-6">
                        <h6>Quick Access:</h6>
                        <ul>
                            <li><strong>Auto Login:</strong> Bypasses login form</li>
                            <li><strong>Working Admin:</strong> Admin dashboard without middleware</li>
                            <li><strong>Simple Dashboard:</strong> Basic auth test</li>
                        </ul>
                        <a href="{{ route('debug.login') }}" class="btn btn-warning btn-sm">
                            <i class="bi bi-lightning"></i> Auto Login & Go to Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .table th {
        border-top: none;
    }
    .card {
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
</style>
@endpush
