@extends('layouts.app')

@section('title', 'Edit User')

@section('sidebar')
<div class="list-group list-group-flush">
    <a href="{{ route('admin.dashboard') }}" class="list-group-item list-group-item-action">
        <i class="bi bi-speedometer2"></i> Dashboard
    </a>
    <a href="{{ route('admin.users') }}" class="list-group-item list-group-item-action active">
        <i class="bi bi-people"></i> Users
    </a>
    <a href="{{ route('admin.tenants') }}" class="list-group-item list-group-item-action">
        <i class="bi bi-building"></i> Organizations
    </a>
    <a href="{{ route('admin.parties') }}" class="list-group-item list-group-item-action">
        <i class="bi bi-flag"></i> Political Parties
    </a>
    <a href="{{ route('admin.constituencies') }}" class="list-group-item list-group-item-action">
        <i class="bi bi-geo-alt"></i> Constituencies
    </a>
    <a href="{{ route('admin.settings') }}" class="list-group-item list-group-item-action">
        <i class="bi bi-gear"></i> Settings
    </a>
</div>
@endsection

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="bi bi-pencil text-primary"></i> Edit User
    </h1>
    <div>
        <a href="{{ route('admin.users') }}" class="btn btn-secondary">
            <i class="bi bi-arrow-left"></i> Back to Users
        </a>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="bi bi-person"></i> User Details
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ route('admin.users.update', $user) }}">
                    @csrf
                    @method('PUT')
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">Full Name *</label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                       id="name" name="name" value="{{ old('name', $user->name) }}" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="mb-3">
                                <label for="email" class="form-label">Email Address *</label>
                                <input type="email" class="form-control @error('email') is-invalid @enderror" 
                                       id="email" name="email" value="{{ old('email', $user->email) }}" required>
                                @error('email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="mb-3">
                                <label for="phone" class="form-label">Phone Number</label>
                                <input type="tel" class="form-control @error('phone') is-invalid @enderror" 
                                       id="phone" name="phone" value="{{ old('phone', $user->phone) }}">
                                @error('phone')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="mb-3">
                                <label for="cnic" class="form-label">CNIC Number</label>
                                <input type="text" class="form-control @error('cnic') is-invalid @enderror" 
                                       id="cnic" name="cnic" value="{{ old('cnic', $user->cnic) }}" maxlength="15">
                                @error('cnic')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="user_type" class="form-label">User Type *</label>
                                <select class="form-select @error('user_type') is-invalid @enderror" 
                                        id="user_type" name="user_type" required>
                                    <option value="admin" {{ old('user_type', $user->user_type) === 'admin' ? 'selected' : '' }}>Admin</option>
                                    <option value="candidate" {{ old('user_type', $user->user_type) === 'candidate' ? 'selected' : '' }}>Candidate</option>
                                    <option value="agent" {{ old('user_type', $user->user_type) === 'agent' ? 'selected' : '' }}>Agent</option>
                                    <option value="volunteer" {{ old('user_type', $user->user_type) === 'volunteer' ? 'selected' : '' }}>Volunteer</option>
                                </select>
                                @error('user_type')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="mb-3">
                                <label for="tenant_id" class="form-label">Organization</label>
                                <select class="form-select @error('tenant_id') is-invalid @enderror" 
                                        id="tenant_id" name="tenant_id">
                                    <option value="">No Organization</option>
                                    @foreach($tenants as $tenant)
                                        <option value="{{ $tenant->id }}" {{ old('tenant_id', $user->tenant_id) === $tenant->id ? 'selected' : '' }}>
                                            {{ $tenant->name['en'] ?? $tenant->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('tenant_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="mb-3">
                                <label for="password" class="form-label">New Password</label>
                                <input type="password" class="form-control @error('password') is-invalid @enderror" 
                                       id="password" name="password">
                                <div class="form-text">Leave blank to keep current password</div>
                                @error('password')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                       {{ old('is_active', $user->is_active) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_active">
                                    User is Active
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="d-flex justify-content-between">
                        <div>
                            <small class="text-muted">
                                <i class="bi bi-info-circle"></i> 
                                Created: {{ $user->created_at->format('M j, Y g:i A') }}
                                @if($user->updated_at != $user->created_at)
                                    | Updated: {{ $user->updated_at->format('M j, Y g:i A') }}
                                @endif
                            </small>
                        </div>
                        <div>
                            <button type="button" class="btn btn-secondary" onclick="history.back()">Cancel</button>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check"></i> Update User
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- User Activity -->
        <div class="card mt-4">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="bi bi-activity"></i> User Activity
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 text-center">
                        <h4 class="text-primary">{{ $user->created_at->diffForHumans() }}</h4>
                        <small class="text-muted">Member Since</small>
                    </div>
                    <div class="col-md-3 text-center">
                        <h4 class="text-success">{{ $user->updated_at->diffForHumans() }}</h4>
                        <small class="text-muted">Last Updated</small>
                    </div>
                    <div class="col-md-3 text-center">
                        <h4 class="text-warning">{{ $user->email_verified_at ? 'Verified' : 'Unverified' }}</h4>
                        <small class="text-muted">Email Status</small>
                    </div>
                    <div class="col-md-3 text-center">
                        <h4 class="text-info">{{ $user->is_active ? 'Active' : 'Inactive' }}</h4>
                        <small class="text-muted">Account Status</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .card {
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
</style>
@endpush

@push('scripts')
<script>
    // Format CNIC input
    document.getElementById('cnic').addEventListener('input', function(e) {
        let value = e.target.value.replace(/\D/g, '');
        if (value.length >= 5) {
            value = value.substring(0, 5) + '-' + value.substring(5);
        }
        if (value.length >= 13) {
            value = value.substring(0, 13) + '-' + value.substring(13, 14);
        }
        e.target.value = value;
    });
</script>
@endpush
