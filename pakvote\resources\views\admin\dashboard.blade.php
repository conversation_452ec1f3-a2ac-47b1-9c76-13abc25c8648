@extends('layouts.app')

@section('title', 'Admin Dashboard')

@section('sidebar')
<div class="list-group list-group-flush">
    <a href="{{ route('admin.dashboard') }}" class="list-group-item list-group-item-action active">
        <i class="bi bi-speedometer2"></i> Dashboard
    </a>
    <a href="{{ route('admin.users') }}" class="list-group-item list-group-item-action">
        <i class="bi bi-people"></i> Users
    </a>
    <a href="{{ route('admin.tenants') }}" class="list-group-item list-group-item-action">
        <i class="bi bi-building"></i> Organizations
    </a>
    <a href="{{ route('admin.parties') }}" class="list-group-item list-group-item-action">
        <i class="bi bi-flag"></i> Political Parties
    </a>
    <a href="{{ route('admin.constituencies') }}" class="list-group-item list-group-item-action">
        <i class="bi bi-geo-alt"></i> Constituencies
    </a>
    <a href="{{ route('admin.settings') }}" class="list-group-item list-group-item-action">
        <i class="bi bi-gear"></i> Settings
    </a>
</div>
@endsection

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="bi bi-speedometer2 text-success"></i> Admin Dashboard
    </h1>
    <div class="text-muted">
        <i class="bi bi-calendar"></i> {{ now()->format('l, F j, Y') }}
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-4 mb-3">
        <div class="card border-primary">
            <div class="card-body text-center">
                <i class="bi bi-building display-4 text-primary"></i>
                <h3 class="mt-2">{{ $stats['total_tenants'] }}</h3>
                <p class="text-muted mb-0">Total Organizations</p>
                <small class="text-success">{{ $stats['active_tenants'] }} Active</small>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-3">
        <div class="card border-info">
            <div class="card-body text-center">
                <i class="bi bi-people display-4 text-info"></i>
                <h3 class="mt-2">{{ $stats['total_users'] }}</h3>
                <p class="text-muted mb-0">Total Users</p>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-3">
        <div class="card border-warning">
            <div class="card-body text-center">
                <i class="bi bi-person-badge display-4 text-warning"></i>
                <h3 class="mt-2">{{ $stats['total_candidates'] }}</h3>
                <p class="text-muted mb-0">Total Candidates</p>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-6 mb-3">
        <div class="card border-success">
            <div class="card-body text-center">
                <i class="bi bi-flag display-4 text-success"></i>
                <h3 class="mt-2">{{ $stats['total_parties'] }}</h3>
                <p class="text-muted mb-0">Political Parties</p>
            </div>
        </div>
    </div>
    <div class="col-md-6 mb-3">
        <div class="card border-danger">
            <div class="card-body text-center">
                <i class="bi bi-geo-alt display-4 text-danger"></i>
                <h3 class="mt-2">{{ $stats['total_constituencies'] }}</h3>
                <p class="text-muted mb-0">Constituencies</p>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-light">
                <h5 class="mb-0">
                    <i class="bi bi-building"></i> Recent Organizations
                </h5>
            </div>
            <div class="card-body">
                @if($recent_tenants->count() > 0)
                    <div class="list-group list-group-flush">
                        @foreach($recent_tenants as $tenant)
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">{{ $tenant->name['en'] ?? $tenant->name }}</h6>
                                    <small class="text-muted">{{ $tenant->domain }}</small>
                                </div>
                                <span class="badge bg-{{ $tenant->is_active ? 'success' : 'secondary' }}">
                                    {{ $tenant->is_active ? 'Active' : 'Inactive' }}
                                </span>
                            </div>
                        @endforeach
                    </div>
                @else
                    <p class="text-muted text-center">No organizations found.</p>
                @endif
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-light">
                <h5 class="mb-0">
                    <i class="bi bi-people"></i> Recent Users
                </h5>
            </div>
            <div class="card-body">
                @if($recent_users->count() > 0)
                    <div class="list-group list-group-flush">
                        @foreach($recent_users as $user)
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">{{ $user->name }}</h6>
                                    <small class="text-muted">{{ $user->email }}</small>
                                </div>
                                <span class="badge bg-{{ $user->is_active ? 'success' : 'secondary' }}">
                                    {{ ucfirst($user->user_type) }}
                                </span>
                            </div>
                        @endforeach
                    </div>
                @else
                    <p class="text-muted text-center">No users found.</p>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="bi bi-lightning"></i> Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="{{ route('admin.users') }}" class="btn btn-outline-primary w-100">
                            <i class="bi bi-person-plus"></i> Manage Users
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ route('admin.tenants') }}" class="btn btn-outline-info w-100">
                            <i class="bi bi-building-add"></i> Manage Organizations
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ route('admin.parties') }}" class="btn btn-outline-success w-100">
                            <i class="bi bi-flag-fill"></i> Manage Parties
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ route('admin.settings') }}" class="btn btn-outline-secondary w-100">
                            <i class="bi bi-gear-fill"></i> System Settings
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .card {
        transition: transform 0.2s;
    }
    .card:hover {
        transform: translateY(-2px);
    }
    .display-4 {
        font-size: 2.5rem;
    }
</style>
@endpush
