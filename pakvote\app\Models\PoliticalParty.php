<?php

namespace App\Models;

use App\Traits\HasUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\Translatable\HasTranslations;

class PoliticalParty extends Model
{
    use HasFactory, HasUuid, HasTranslations;

    protected $fillable = [
        'name',
        'short_name',
        'symbol',
        'symbol_image',
        'description',
        'founded_date',
        'headquarters',
        'website',
        'contact_email',
        'contact_phone',
        'is_active',
        'ecp_registration_number',
        'party_head',
    ];

    protected $casts = [
        'founded_date' => 'date',
        'is_active' => 'boolean',
    ];

    public $translatable = ['name', 'description'];

    /**
     * Get the candidates for the party.
     */
    public function candidates(): Has<PERSON>any
    {
        return $this->hasMany(Candidate::class);
    }

    /**
     * Get the active candidates for the party.
     */
    public function activeCandidates(): Has<PERSON><PERSON>
    {
        return $this->hasMany(Candidate::class)->where('is_active', true);
    }

    /**
     * Scope for active parties
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get full party name with symbol
     */
    public function getFullNameAttribute(): string
    {
        return $this->name . ' (' . $this->symbol . ')';
    }
}
