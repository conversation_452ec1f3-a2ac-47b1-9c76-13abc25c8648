<?php $__env->startSection('title', 'Modern UI Components Demo'); ?>

<?php $__env->startSection('sidebar'); ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="demo-container">
    <!-- Page Header -->
    <div class="page-header mb-5">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="display-4 fw-bold text-primary">
                    <i class="bi bi-palette"></i> Modern UI Components
                </h1>
                <p class="lead text-muted">
                    Showcase of beautiful, modern, and creative UI components for PakVote Election Management System
                </p>
            </div>
            <div class="col-md-4 text-end">
                <div class="demo-badge">
                    <span class="badge bg-success fs-6 px-3 py-2">
                        <i class="bi bi-check-circle"></i> Demo Mode
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards Demo -->
    <div class="section mb-5">
        <h2 class="section-title">
            <i class="bi bi-graph-up text-primary"></i> Statistics Cards
        </h2>
        <p class="section-description">Modern cards with statistics, hover effects, and gradients</p>
        
        <div class="row">
            <div class="col-lg-3 col-md-6 mb-4">
                <?php if (isset($component)) { $__componentOriginal53747ceb358d30c0105769f8471417f6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal53747ceb358d30c0105769f8471417f6 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.card','data' => ['title' => 'Total Users','icon' => 'bi bi-people','headerClass' => 'bg-primary text-white','hover' => 'true','stats' => [
                        [
                            'value' => '1,234',
                            'label' => 'Registered Users',
                            'icon' => 'bi bi-person-plus',
                            'change' => 12
                        ]
                    ]]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Total Users','icon' => 'bi bi-people','headerClass' => 'bg-primary text-white','hover' => 'true','stats' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
                        [
                            'value' => '1,234',
                            'label' => 'Registered Users',
                            'icon' => 'bi bi-person-plus',
                            'change' => 12
                        ]
                    ])]); ?>
                    <div class="text-center">
                        <small class="text-muted">Growing steadily</small>
                    </div>
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal53747ceb358d30c0105769f8471417f6)): ?>
<?php $attributes = $__attributesOriginal53747ceb358d30c0105769f8471417f6; ?>
<?php unset($__attributesOriginal53747ceb358d30c0105769f8471417f6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal53747ceb358d30c0105769f8471417f6)): ?>
<?php $component = $__componentOriginal53747ceb358d30c0105769f8471417f6; ?>
<?php unset($__componentOriginal53747ceb358d30c0105769f8471417f6); ?>
<?php endif; ?>
            </div>

            <div class="col-lg-3 col-md-6 mb-4">
                <?php if (isset($component)) { $__componentOriginal53747ceb358d30c0105769f8471417f6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal53747ceb358d30c0105769f8471417f6 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.card','data' => ['title' => 'Organizations','icon' => 'bi bi-building','headerClass' => 'bg-success text-white','hover' => 'true','gradient' => 'true','stats' => [
                        [
                            'value' => '89',
                            'label' => 'Active Organizations',
                            'icon' => 'bi bi-building-add',
                            'change' => 8
                        ]
                    ]]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Organizations','icon' => 'bi bi-building','headerClass' => 'bg-success text-white','hover' => 'true','gradient' => 'true','stats' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
                        [
                            'value' => '89',
                            'label' => 'Active Organizations',
                            'icon' => 'bi bi-building-add',
                            'change' => 8
                        ]
                    ])]); ?>
                    <div class="text-center">
                        <small class="text-white">Expanding network</small>
                    </div>
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal53747ceb358d30c0105769f8471417f6)): ?>
<?php $attributes = $__attributesOriginal53747ceb358d30c0105769f8471417f6; ?>
<?php unset($__attributesOriginal53747ceb358d30c0105769f8471417f6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal53747ceb358d30c0105769f8471417f6)): ?>
<?php $component = $__componentOriginal53747ceb358d30c0105769f8471417f6; ?>
<?php unset($__componentOriginal53747ceb358d30c0105769f8471417f6); ?>
<?php endif; ?>
            </div>

            <div class="col-lg-3 col-md-6 mb-4">
                <?php if (isset($component)) { $__componentOriginal53747ceb358d30c0105769f8471417f6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal53747ceb358d30c0105769f8471417f6 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.card','data' => ['title' => 'Political Parties','icon' => 'bi bi-flag','headerClass' => 'bg-warning text-dark','hover' => 'true','stats' => [
                        [
                            'value' => '45',
                            'label' => 'Registered Parties',
                            'icon' => 'bi bi-flag-fill',
                            'change' => -2
                        ]
                    ]]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Political Parties','icon' => 'bi bi-flag','headerClass' => 'bg-warning text-dark','hover' => 'true','stats' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
                        [
                            'value' => '45',
                            'label' => 'Registered Parties',
                            'icon' => 'bi bi-flag-fill',
                            'change' => -2
                        ]
                    ])]); ?>
                    <div class="text-center">
                        <small class="text-muted">Democratic diversity</small>
                    </div>
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal53747ceb358d30c0105769f8471417f6)): ?>
<?php $attributes = $__attributesOriginal53747ceb358d30c0105769f8471417f6; ?>
<?php unset($__attributesOriginal53747ceb358d30c0105769f8471417f6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal53747ceb358d30c0105769f8471417f6)): ?>
<?php $component = $__componentOriginal53747ceb358d30c0105769f8471417f6; ?>
<?php unset($__componentOriginal53747ceb358d30c0105769f8471417f6); ?>
<?php endif; ?>
            </div>

            <div class="col-lg-3 col-md-6 mb-4">
                <?php if (isset($component)) { $__componentOriginal53747ceb358d30c0105769f8471417f6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal53747ceb358d30c0105769f8471417f6 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.card','data' => ['title' => 'Constituencies','icon' => 'bi bi-geo-alt','headerClass' => 'bg-info text-white','hover' => 'true','shadow' => 'heavy','stats' => [
                        [
                            'value' => '272',
                            'label' => 'Total Constituencies',
                            'icon' => 'bi bi-geo-alt-fill',
                            'change' => 0
                        ]
                    ]]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Constituencies','icon' => 'bi bi-geo-alt','headerClass' => 'bg-info text-white','hover' => 'true','shadow' => 'heavy','stats' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
                        [
                            'value' => '272',
                            'label' => 'Total Constituencies',
                            'icon' => 'bi bi-geo-alt-fill',
                            'change' => 0
                        ]
                    ])]); ?>
                    <div class="text-center">
                        <small class="text-muted">Complete coverage</small>
                    </div>
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal53747ceb358d30c0105769f8471417f6)): ?>
<?php $attributes = $__attributesOriginal53747ceb358d30c0105769f8471417f6; ?>
<?php unset($__attributesOriginal53747ceb358d30c0105769f8471417f6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal53747ceb358d30c0105769f8471417f6)): ?>
<?php $component = $__componentOriginal53747ceb358d30c0105769f8471417f6; ?>
<?php unset($__componentOriginal53747ceb358d30c0105769f8471417f6); ?>
<?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Content Cards Demo -->
    <div class="section mb-5">
        <h2 class="section-title">
            <i class="bi bi-card-text text-success"></i> Content Cards
        </h2>
        <p class="section-description">Flexible cards for various content types</p>
        
        <div class="row">
            <div class="col-lg-6 mb-4">
                <?php if (isset($component)) { $__componentOriginal53747ceb358d30c0105769f8471417f6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal53747ceb358d30c0105769f8471417f6 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.card','data' => ['title' => 'System Status','subtitle' => 'Real-time monitoring','icon' => 'bi bi-shield-check','headerClass' => 'bg-success text-white']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'System Status','subtitle' => 'Real-time monitoring','icon' => 'bi bi-shield-check','headerClass' => 'bg-success text-white']); ?>
                     <?php $__env->slot('actions', null, []); ?> 
                        <button class="btn btn-sm btn-outline-light">
                            <i class="bi bi-arrow-clockwise"></i> Refresh
                        </button>
                     <?php $__env->endSlot(); ?>
                    
                    <div class="status-grid">
                        <div class="status-item">
                            <div class="status-indicator online"></div>
                            <div class="status-details">
                                <strong>Database</strong>
                                <small class="text-muted d-block">Connected & Optimized</small>
                            </div>
                            <div class="status-badge">
                                <span class="badge bg-success">99.9%</span>
                            </div>
                        </div>
                        
                        <div class="status-item">
                            <div class="status-indicator online"></div>
                            <div class="status-details">
                                <strong>Email Service</strong>
                                <small class="text-muted d-block">Operational</small>
                            </div>
                            <div class="status-badge">
                                <span class="badge bg-success">Active</span>
                            </div>
                        </div>
                        
                        <div class="status-item">
                            <div class="status-indicator warning"></div>
                            <div class="status-details">
                                <strong>ECP Integration</strong>
                                <small class="text-muted d-block">Testing Phase</small>
                            </div>
                            <div class="status-badge">
                                <span class="badge bg-warning">Beta</span>
                            </div>
                        </div>
                    </div>
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal53747ceb358d30c0105769f8471417f6)): ?>
<?php $attributes = $__attributesOriginal53747ceb358d30c0105769f8471417f6; ?>
<?php unset($__attributesOriginal53747ceb358d30c0105769f8471417f6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal53747ceb358d30c0105769f8471417f6)): ?>
<?php $component = $__componentOriginal53747ceb358d30c0105769f8471417f6; ?>
<?php unset($__componentOriginal53747ceb358d30c0105769f8471417f6); ?>
<?php endif; ?>
            </div>

            <div class="col-lg-6 mb-4">
                <?php if (isset($component)) { $__componentOriginal53747ceb358d30c0105769f8471417f6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal53747ceb358d30c0105769f8471417f6 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.card','data' => ['title' => 'Recent Activity','icon' => 'bi bi-activity','headerClass' => 'bg-dark text-white']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Recent Activity','icon' => 'bi bi-activity','headerClass' => 'bg-dark text-white']); ?>
                    <div class="activity-feed">
                        <div class="activity-item">
                            <div class="activity-icon bg-primary">
                                <i class="bi bi-person-plus"></i>
                            </div>
                            <div class="activity-content">
                                <strong>New user registered</strong>
                                <small class="text-muted d-block">Ahmed Ali joined as candidate</small>
                                <small class="text-muted">2 minutes ago</small>
                            </div>
                        </div>
                        
                        <div class="activity-item">
                            <div class="activity-icon bg-success">
                                <i class="bi bi-building-add"></i>
                            </div>
                            <div class="activity-content">
                                <strong>Organization verified</strong>
                                <small class="text-muted d-block">PTI Karachi approved</small>
                                <small class="text-muted">15 minutes ago</small>
                            </div>
                        </div>
                        
                        <div class="activity-item">
                            <div class="activity-icon bg-warning">
                                <i class="bi bi-flag"></i>
                            </div>
                            <div class="activity-content">
                                <strong>Party registration</strong>
                                <small class="text-muted d-block">New political party submitted</small>
                                <small class="text-muted">1 hour ago</small>
                            </div>
                        </div>
                    </div>
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal53747ceb358d30c0105769f8471417f6)): ?>
<?php $attributes = $__attributesOriginal53747ceb358d30c0105769f8471417f6; ?>
<?php unset($__attributesOriginal53747ceb358d30c0105769f8471417f6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal53747ceb358d30c0105769f8471417f6)): ?>
<?php $component = $__componentOriginal53747ceb358d30c0105769f8471417f6; ?>
<?php unset($__componentOriginal53747ceb358d30c0105769f8471417f6); ?>
<?php endif; ?>
            </div>
        </div>
    </div>

    <!-- UI Elements Demo -->
    <div class="section mb-5">
        <h2 class="section-title">
            <i class="bi bi-ui-checks text-warning"></i> UI Elements
        </h2>
        <p class="section-description">Modern buttons, forms, and interactive elements</p>
        
        <div class="row">
            <div class="col-lg-6 mb-4">
                <?php if (isset($component)) { $__componentOriginal53747ceb358d30c0105769f8471417f6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal53747ceb358d30c0105769f8471417f6 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.card','data' => ['title' => 'Buttons & Actions','icon' => 'bi bi-mouse','headerClass' => 'bg-primary text-white']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Buttons & Actions','icon' => 'bi bi-mouse','headerClass' => 'bg-primary text-white']); ?>
                    <div class="button-showcase">
                        <div class="mb-3">
                            <h6>Primary Actions</h6>
                            <div class="d-flex gap-2 flex-wrap">
                                <button class="btn btn-primary">
                                    <i class="bi bi-plus"></i> Add New
                                </button>
                                <button class="btn btn-outline-primary">
                                    <i class="bi bi-pencil"></i> Edit
                                </button>
                                <button class="btn btn-success">
                                    <i class="bi bi-check"></i> Approve
                                </button>
                                <button class="btn btn-danger">
                                    <i class="bi bi-trash"></i> Delete
                                </button>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <h6>Size Variants</h6>
                            <div class="d-flex gap-2 align-items-center flex-wrap">
                                <button class="btn btn-primary btn-sm">Small</button>
                                <button class="btn btn-primary">Regular</button>
                                <button class="btn btn-primary btn-lg">Large</button>
                            </div>
                        </div>
                    </div>
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal53747ceb358d30c0105769f8471417f6)): ?>
<?php $attributes = $__attributesOriginal53747ceb358d30c0105769f8471417f6; ?>
<?php unset($__attributesOriginal53747ceb358d30c0105769f8471417f6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal53747ceb358d30c0105769f8471417f6)): ?>
<?php $component = $__componentOriginal53747ceb358d30c0105769f8471417f6; ?>
<?php unset($__componentOriginal53747ceb358d30c0105769f8471417f6); ?>
<?php endif; ?>
            </div>

            <div class="col-lg-6 mb-4">
                <?php if (isset($component)) { $__componentOriginal53747ceb358d30c0105769f8471417f6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal53747ceb358d30c0105769f8471417f6 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.card','data' => ['title' => 'Form Elements','icon' => 'bi bi-input-cursor','headerClass' => 'bg-info text-white']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Form Elements','icon' => 'bi bi-input-cursor','headerClass' => 'bg-info text-white']); ?>
                    <form class="form-showcase">
                        <div class="mb-3">
                            <label class="form-label">Text Input</label>
                            <input type="text" class="form-control" placeholder="Enter your name">
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Select Dropdown</label>
                            <select class="form-select">
                                <option>Choose option...</option>
                                <option>Option 1</option>
                                <option>Option 2</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Textarea</label>
                            <textarea class="form-control" rows="3" placeholder="Enter description..."></textarea>
                        </div>
                        
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="demoCheck">
                            <label class="form-check-label" for="demoCheck">
                                I agree to the terms and conditions
                            </label>
                        </div>
                    </form>
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal53747ceb358d30c0105769f8471417f6)): ?>
<?php $attributes = $__attributesOriginal53747ceb358d30c0105769f8471417f6; ?>
<?php unset($__attributesOriginal53747ceb358d30c0105769f8471417f6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal53747ceb358d30c0105769f8471417f6)): ?>
<?php $component = $__componentOriginal53747ceb358d30c0105769f8471417f6; ?>
<?php unset($__componentOriginal53747ceb358d30c0105769f8471417f6); ?>
<?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Color Palette Demo -->
    <div class="section mb-5">
        <h2 class="section-title">
            <i class="bi bi-palette text-danger"></i> Color Palette
        </h2>
        <p class="section-description">Pakistani-themed color scheme with modern gradients</p>
        
        <?php if (isset($component)) { $__componentOriginal53747ceb358d30c0105769f8471417f6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal53747ceb358d30c0105769f8471417f6 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.card','data' => ['title' => 'Brand Colors','icon' => 'bi bi-droplet','headerClass' => 'bg-secondary text-white']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Brand Colors','icon' => 'bi bi-droplet','headerClass' => 'bg-secondary text-white']); ?>
            <div class="color-palette">
                <div class="color-item">
                    <div class="color-swatch" style="background: #2d5a27;"></div>
                    <div class="color-info">
                        <strong>Primary Green</strong>
                        <small class="text-muted d-block">#2d5a27</small>
                    </div>
                </div>
                
                <div class="color-item">
                    <div class="color-swatch" style="background: #4a7c59;"></div>
                    <div class="color-info">
                        <strong>Primary Light</strong>
                        <small class="text-muted d-block">#4a7c59</small>
                    </div>
                </div>
                
                <div class="color-item">
                    <div class="color-swatch" style="background: #1e3d1a;"></div>
                    <div class="color-info">
                        <strong>Primary Dark</strong>
                        <small class="text-muted d-block">#1e3d1a</small>
                    </div>
                </div>
                
                <div class="color-item">
                    <div class="color-swatch" style="background: #28a745;"></div>
                    <div class="color-info">
                        <strong>Accent Green</strong>
                        <small class="text-muted d-block">#28a745</small>
                    </div>
                </div>
            </div>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal53747ceb358d30c0105769f8471417f6)): ?>
<?php $attributes = $__attributesOriginal53747ceb358d30c0105769f8471417f6; ?>
<?php unset($__attributesOriginal53747ceb358d30c0105769f8471417f6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal53747ceb358d30c0105769f8471417f6)): ?>
<?php $component = $__componentOriginal53747ceb358d30c0105769f8471417f6; ?>
<?php unset($__componentOriginal53747ceb358d30c0105769f8471417f6); ?>
<?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.demo-container {
    max-width: 1200px;
    margin: 0 auto;
}

.page-header {
    background: linear-gradient(135deg, rgba(45, 90, 39, 0.1) 0%, rgba(40, 167, 69, 0.1) 100%);
    padding: 40px;
    border-radius: 16px;
    margin-bottom: 40px;
}

.section-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 8px;
    color: var(--text-primary);
}

.section-description {
    color: var(--text-secondary);
    margin-bottom: 30px;
}

.status-grid {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: var(--secondary-color);
    border-radius: 8px;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    flex-shrink: 0;
}

.status-indicator.online {
    background: #28a745;
    animation: pulse 2s infinite;
}

.status-indicator.warning {
    background: #ffc107;
}

.status-details {
    flex: 1;
}

.activity-feed {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
}

.activity-icon {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.9rem;
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
}

.button-showcase h6 {
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: 10px;
}

.color-palette {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.color-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px;
    background: var(--secondary-color);
    border-radius: 8px;
}

.color-swatch {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    flex-shrink: 0;
    box-shadow: var(--shadow-light);
}

.demo-badge {
    text-align: center;
}

@media (max-width: 768px) {
    .page-header {
        padding: 20px;
        text-align: center;
    }
    
    .color-palette {
        grid-template-columns: 1fr;
    }
    
    .activity-item,
    .status-item {
        flex-direction: column;
        text-align: center;
        gap: 8px;
    }
}
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.modern', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\ECP\pakvote\resources\views/demo/modern-ui.blade.php ENDPATH**/ ?>