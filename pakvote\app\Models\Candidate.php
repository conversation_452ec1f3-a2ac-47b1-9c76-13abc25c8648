<?php

namespace App\Models;

use App\Traits\HasUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\Translatable\HasTranslations;

class Candidate extends Model
{
    use HasFactory, HasUuid, HasTranslations;

    protected $fillable = [
        'user_id',
        'tenant_id',
        'political_party_id',
        'constituency_id',
        'candidate_name',
        'father_name',
        'cnic',
        'date_of_birth',
        'gender',
        'education',
        'profession',
        'address',
        'phone',
        'email',
        'profile_image',
        'manifesto',
        'election_symbol',
        'symbol_image',
        'nomination_status',
        'nomination_date',
        'campaign_budget',
        'is_active',
        'ecp_nomination_number',
        'social_media_links',
    ];

    protected $casts = [
        'date_of_birth' => 'date',
        'nomination_date' => 'date',
        'is_active' => 'boolean',
        'campaign_budget' => 'decimal:2',
        'social_media_links' => 'array',
    ];

    public $translatable = ['manifesto'];

    /**
     * Nomination statuses
     */
    const STATUS_PENDING = 'pending';
    const STATUS_APPROVED = 'approved';
    const STATUS_REJECTED = 'rejected';
    const STATUS_WITHDRAWN = 'withdrawn';

    /**
     * Gender options
     */
    const GENDER_MALE = 'male';
    const GENDER_FEMALE = 'female';
    const GENDER_OTHER = 'other';

    /**
     * Get the user that owns the candidate.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the tenant that owns the candidate.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    /**
     * Get the political party that owns the candidate.
     */
    public function politicalParty(): BelongsTo
    {
        return $this->belongsTo(PoliticalParty::class);
    }

    /**
     * Get the constituency that owns the candidate.
     */
    public function constituency(): BelongsTo
    {
        return $this->belongsTo(Constituency::class);
    }

    /**
     * Get the campaigns for the candidate.
     */
    public function campaigns(): HasMany
    {
        return $this->hasMany(Campaign::class);
    }

    /**
     * Scope for active candidates
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for approved candidates
     */
    public function scopeApproved($query)
    {
        return $query->where('nomination_status', self::STATUS_APPROVED);
    }

    /**
     * Check if candidate is approved
     */
    public function isApproved(): bool
    {
        return $this->nomination_status === self::STATUS_APPROVED;
    }

    /**
     * Get full candidate name with party
     */
    public function getFullNameWithPartyAttribute(): string
    {
        $partyName = $this->politicalParty ? $this->politicalParty->short_name : 'Independent';
        return $this->candidate_name . ' (' . $partyName . ')';
    }
}
