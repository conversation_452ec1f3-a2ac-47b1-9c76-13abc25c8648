@extends('layouts.app')

@section('title', 'Edit Organization')

@section('sidebar')
<div class="list-group list-group-flush">
    <a href="{{ route('admin.dashboard') }}" class="list-group-item list-group-item-action">
        <i class="bi bi-speedometer2"></i> Dashboard
    </a>
    <a href="{{ route('admin.users') }}" class="list-group-item list-group-item-action">
        <i class="bi bi-people"></i> Users
    </a>
    <a href="{{ route('admin.tenants') }}" class="list-group-item list-group-item-action active">
        <i class="bi bi-building"></i> Organizations
    </a>
    <a href="{{ route('admin.parties') }}" class="list-group-item list-group-item-action">
        <i class="bi bi-flag"></i> Political Parties
    </a>
    <a href="{{ route('admin.constituencies') }}" class="list-group-item list-group-item-action">
        <i class="bi bi-geo-alt"></i> Constituencies
    </a>
    <a href="{{ route('admin.settings') }}" class="list-group-item list-group-item-action">
        <i class="bi bi-gear"></i> Settings
    </a>
</div>
@endsection

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="bi bi-pencil text-primary"></i> Edit Organization
    </h1>
    <div>
        <a href="{{ route('admin.tenants') }}" class="btn btn-secondary">
            <i class="bi bi-arrow-left"></i> Back to Organizations
        </a>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="bi bi-building"></i> Organization Details
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ route('admin.tenants.update', $tenant) }}">
                    @csrf
                    @method('PUT')
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">Organization Name *</label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                       id="name" name="name" value="{{ old('name', $tenant->name['en'] ?? $tenant->name) }}" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="mb-3">
                                <label for="domain" class="form-label">Domain *</label>
                                <input type="text" class="form-control @error('domain') is-invalid @enderror" 
                                       id="domain" name="domain" value="{{ old('domain', $tenant->domain) }}" required>
                                @error('domain')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="mb-3">
                                <label for="contact_email" class="form-label">Contact Email *</label>
                                <input type="email" class="form-control @error('contact_email') is-invalid @enderror" 
                                       id="contact_email" name="contact_email" value="{{ old('contact_email', $tenant->contact_email) }}" required>
                                @error('contact_email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="mb-3">
                                <label for="contact_phone" class="form-label">Contact Phone</label>
                                <input type="tel" class="form-control @error('contact_phone') is-invalid @enderror" 
                                       id="contact_phone" name="contact_phone" value="{{ old('contact_phone', $tenant->contact_phone) }}">
                                @error('contact_phone')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="subscription_plan" class="form-label">Subscription Plan *</label>
                                <select class="form-select @error('subscription_plan') is-invalid @enderror" 
                                        id="subscription_plan" name="subscription_plan" required>
                                    <option value="free" {{ old('subscription_plan', $tenant->subscription_plan) === 'free' ? 'selected' : '' }}>Free</option>
                                    <option value="pro" {{ old('subscription_plan', $tenant->subscription_plan) === 'pro' ? 'selected' : '' }}>Pro</option>
                                    <option value="enterprise" {{ old('subscription_plan', $tenant->subscription_plan) === 'enterprise' ? 'selected' : '' }}>Enterprise</option>
                                </select>
                                @error('subscription_plan')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="mb-3">
                                <label for="subscription_status" class="form-label">Subscription Status *</label>
                                <select class="form-select @error('subscription_status') is-invalid @enderror" 
                                        id="subscription_status" name="subscription_status" required>
                                    <option value="active" {{ old('subscription_status', $tenant->subscription_status) === 'active' ? 'selected' : '' }}>Active</option>
                                    <option value="inactive" {{ old('subscription_status', $tenant->subscription_status) === 'inactive' ? 'selected' : '' }}>Inactive</option>
                                    <option value="suspended" {{ old('subscription_status', $tenant->subscription_status) === 'suspended' ? 'selected' : '' }}>Suspended</option>
                                    <option value="cancelled" {{ old('subscription_status', $tenant->subscription_status) === 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                                </select>
                                @error('subscription_status')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="mb-3">
                                <label for="address" class="form-label">Address</label>
                                <textarea class="form-control @error('address') is-invalid @enderror" 
                                          id="address" name="address" rows="3">{{ old('address', $tenant->address) }}</textarea>
                                @error('address')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                       {{ old('is_active', $tenant->is_active) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_active">
                                    Organization is Active
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="d-flex justify-content-between">
                        <div>
                            <small class="text-muted">
                                <i class="bi bi-info-circle"></i> 
                                Created: {{ $tenant->created_at->format('M j, Y g:i A') }}
                                @if($tenant->updated_at != $tenant->created_at)
                                    | Updated: {{ $tenant->updated_at->format('M j, Y g:i A') }}
                                @endif
                            </small>
                        </div>
                        <div>
                            <button type="button" class="btn btn-secondary" onclick="history.back()">Cancel</button>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check"></i> Update Organization
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Organization Statistics -->
        <div class="card mt-4">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="bi bi-graph-up"></i> Organization Statistics
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 text-center">
                        <h4 class="text-primary">{{ $tenant->users()->count() }}</h4>
                        <small class="text-muted">Total Users</small>
                    </div>
                    <div class="col-md-3 text-center">
                        <h4 class="text-success">{{ $tenant->users()->where('is_active', true)->count() }}</h4>
                        <small class="text-muted">Active Users</small>
                    </div>
                    <div class="col-md-3 text-center">
                        <h4 class="text-warning">{{ $tenant->users()->where('user_type', 'candidate')->count() }}</h4>
                        <small class="text-muted">Candidates</small>
                    </div>
                    <div class="col-md-3 text-center">
                        <h4 class="text-info">{{ $tenant->users()->where('user_type', 'volunteer')->count() }}</h4>
                        <small class="text-muted">Volunteers</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .card {
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
</style>
@endpush
