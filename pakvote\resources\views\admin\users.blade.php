@extends('layouts.app')

@section('title', 'Manage Users')

@section('sidebar')
<div class="list-group list-group-flush">
    <a href="{{ route('admin.dashboard') }}" class="list-group-item list-group-item-action">
        <i class="bi bi-speedometer2"></i> Dashboard
    </a>
    <a href="{{ route('admin.users') }}" class="list-group-item list-group-item-action active">
        <i class="bi bi-people"></i> Users
    </a>
    <a href="{{ route('admin.tenants') }}" class="list-group-item list-group-item-action">
        <i class="bi bi-building"></i> Organizations
    </a>
    <a href="{{ route('admin.parties') }}" class="list-group-item list-group-item-action">
        <i class="bi bi-flag"></i> Political Parties
    </a>
    <a href="{{ route('admin.constituencies') }}" class="list-group-item list-group-item-action">
        <i class="bi bi-geo-alt"></i> Constituencies
    </a>
    <a href="{{ route('admin.settings') }}" class="list-group-item list-group-item-action">
        <i class="bi bi-gear"></i> Settings
    </a>
</div>
@endsection

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="bi bi-people text-primary"></i> Manage Users
    </h1>
    <div>
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
            <i class="bi bi-person-plus"></i> Add User
        </button>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card border-primary">
            <div class="card-body text-center">
                <i class="bi bi-people display-6 text-primary"></i>
                <h4 class="mt-2">{{ $users->total() }}</h4>
                <p class="text-muted mb-0">Total Users</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-success">
            <div class="card-body text-center">
                <i class="bi bi-person-check display-6 text-success"></i>
                <h4 class="mt-2">{{ $users->where('is_active', true)->count() }}</h4>
                <p class="text-muted mb-0">Active Users</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-warning">
            <div class="card-body text-center">
                <i class="bi bi-person-badge display-6 text-warning"></i>
                <h4 class="mt-2">{{ $users->where('user_type', 'candidate')->count() }}</h4>
                <p class="text-muted mb-0">Candidates</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-info">
            <div class="card-body text-center">
                <i class="bi bi-shield display-6 text-info"></i>
                <h4 class="mt-2">{{ $users->where('user_type', 'admin')->count() }}</h4>
                <p class="text-muted mb-0">Admins</p>
            </div>
        </div>
    </div>
</div>

<!-- Users Table -->
<div class="card">
    <div class="card-header bg-light">
        <h5 class="mb-0">
            <i class="bi bi-list"></i> Users List
        </h5>
    </div>
    <div class="card-body">
        @if($users->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>User</th>
                            <th>Contact</th>
                            <th>Organization</th>
                            <th>Type</th>
                            <th>Status</th>
                            <th>Joined</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($users as $user)
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        @if($user->profile_image)
                                            <img src="{{ $user->profile_image }}" alt="Profile" class="rounded-circle me-2" width="40" height="40">
                                        @else
                                            <div class="bg-secondary text-white rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 40px; height: 40px;">
                                                {{ strtoupper(substr($user->name, 0, 1)) }}
                                            </div>
                                        @endif
                                        <div>
                                            <h6 class="mb-0">{{ $user->name }}</h6>
                                            <small class="text-muted">{{ substr($user->id, 0, 8) }}...</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <small class="d-block">{{ $user->email }}</small>
                                        @if($user->phone)
                                            <small class="text-muted">{{ $user->phone }}</small>
                                        @endif
                                        @if($user->cnic)
                                            <small class="text-muted d-block">CNIC: {{ $user->cnic }}</small>
                                        @endif
                                    </div>
                                </td>
                                <td>
                                    @if($user->tenant)
                                        <div>
                                            <small class="d-block fw-bold">{{ $user->tenant->name['en'] ?? $user->tenant->name }}</small>
                                            <small class="text-muted">{{ $user->tenant->domain }}</small>
                                        </div>
                                    @else
                                        <span class="text-muted">No Organization</span>
                                    @endif
                                </td>
                                <td>
                                    <span class="badge bg-{{ $user->user_type === 'admin' ? 'danger' : ($user->user_type === 'candidate' ? 'primary' : 'secondary') }}">
                                        {{ ucfirst($user->user_type) }}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-{{ $user->is_active ? 'success' : 'secondary' }}">
                                        {{ $user->is_active ? 'Active' : 'Inactive' }}
                                    </span>
                                    @if($user->email_verified_at)
                                        <br><small class="text-success">✓ Verified</small>
                                    @else
                                        <br><small class="text-warning">⚠ Unverified</small>
                                    @endif
                                </td>
                                <td>
                                    <small>{{ $user->created_at->format('M j, Y') }}</small>
                                    <br>
                                    <small class="text-muted">{{ $user->created_at->diffForHumans() }}</small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button type="button" class="btn btn-outline-primary" title="Edit">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-info" title="View Profile">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-{{ $user->is_active ? 'warning' : 'success' }}" title="{{ $user->is_active ? 'Deactivate' : 'Activate' }}">
                                            <i class="bi bi-{{ $user->is_active ? 'pause' : 'play' }}"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-4">
                {{ $users->links() }}
            </div>
        @else
            <div class="text-center py-5">
                <i class="bi bi-people display-1 text-muted"></i>
                <h4 class="text-muted mt-3">No Users Found</h4>
                <p class="text-muted">Start by adding your first user.</p>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                    <i class="bi bi-person-plus"></i> Add User
                </button>
            </div>
        @endif
    </div>
</div>

<!-- Add User Modal -->
<div class="modal fade" id="addUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New User</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="user_name" class="form-label">Full Name</label>
                        <input type="text" class="form-control" id="user_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="user_email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="user_email" required>
                    </div>
                    <div class="mb-3">
                        <label for="user_phone" class="form-label">Phone</label>
                        <input type="tel" class="form-control" id="user_phone">
                    </div>
                    <div class="mb-3">
                        <label for="user_cnic" class="form-label">CNIC</label>
                        <input type="text" class="form-control" id="user_cnic">
                    </div>
                    <div class="mb-3">
                        <label for="user_type" class="form-label">User Type</label>
                        <select class="form-select" id="user_type">
                            <option value="candidate">Candidate</option>
                            <option value="agent">Agent</option>
                            <option value="volunteer">Volunteer</option>
                            <option value="admin">Admin</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="user_password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="user_password" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add User</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .table th {
        border-top: none;
        font-weight: 600;
    }
    .card {
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
</style>
@endpush
