<?php $__env->startSection('title', 'System Settings'); ?>

<?php $__env->startSection('sidebar'); ?>
<div class="list-group list-group-flush">
    <a href="<?php echo e(route('admin.dashboard')); ?>" class="list-group-item list-group-item-action">
        <i class="bi bi-speedometer2"></i> Dashboard
    </a>
    <a href="<?php echo e(route('admin.users')); ?>" class="list-group-item list-group-item-action">
        <i class="bi bi-people"></i> Users
    </a>
    <a href="<?php echo e(route('admin.tenants')); ?>" class="list-group-item list-group-item-action">
        <i class="bi bi-building"></i> Organizations
    </a>
    <a href="<?php echo e(route('admin.parties')); ?>" class="list-group-item list-group-item-action">
        <i class="bi bi-flag"></i> Political Parties
    </a>
    <a href="<?php echo e(route('admin.constituencies')); ?>" class="list-group-item list-group-item-action">
        <i class="bi bi-geo-alt"></i> Constituencies
    </a>
    <a href="<?php echo e(route('admin.settings')); ?>" class="list-group-item list-group-item-action active">
        <i class="bi bi-gear"></i> Settings
    </a>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="bi bi-gear text-secondary"></i> System Settings
    </h1>
</div>

<div class="row">
    <!-- General Settings -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="bi bi-gear"></i> General Settings
                </h5>
            </div>
            <div class="card-body">
                <form>
                    <div class="mb-3">
                        <label for="app_name" class="form-label">Application Name</label>
                        <input type="text" class="form-control" id="app_name" value="PakVote - Election Management System">
                    </div>
                    <div class="mb-3">
                        <label for="app_url" class="form-label">Application URL</label>
                        <input type="url" class="form-control" id="app_url" value="http://localhost:8000">
                    </div>
                    <div class="mb-3">
                        <label for="default_language" class="form-label">Default Language</label>
                        <select class="form-select" id="default_language">
                            <option value="en" selected>English</option>
                            <option value="ur">Urdu</option>
                            <option value="sd">Sindhi</option>
                            <option value="ps">Pashto</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="timezone" class="form-label">Timezone</label>
                        <select class="form-select" id="timezone">
                            <option value="Asia/Karachi" selected>Asia/Karachi (PKT)</option>
                            <option value="UTC">UTC</option>
                        </select>
                    </div>
                    <button type="submit" class="btn btn-primary">Save General Settings</button>
                </form>
            </div>
        </div>
    </div>

    <!-- Email Settings -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="bi bi-envelope"></i> Email Settings
                </h5>
            </div>
            <div class="card-body">
                <form>
                    <div class="mb-3">
                        <label for="mail_driver" class="form-label">Mail Driver</label>
                        <select class="form-select" id="mail_driver">
                            <option value="smtp" selected>SMTP</option>
                            <option value="sendmail">Sendmail</option>
                            <option value="mailgun">Mailgun</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="mail_host" class="form-label">SMTP Host</label>
                        <input type="text" class="form-control" id="mail_host" placeholder="smtp.gmail.com">
                    </div>
                    <div class="mb-3">
                        <label for="mail_port" class="form-label">SMTP Port</label>
                        <input type="number" class="form-control" id="mail_port" value="587">
                    </div>
                    <div class="mb-3">
                        <label for="mail_from" class="form-label">From Email</label>
                        <input type="email" class="form-control" id="mail_from" placeholder="<EMAIL>">
                    </div>
                    <button type="submit" class="btn btn-info">Save Email Settings</button>
                </form>
            </div>
        </div>
    </div>

    <!-- SMS Settings -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="bi bi-phone"></i> SMS Settings
                </h5>
            </div>
            <div class="card-body">
                <form>
                    <div class="mb-3">
                        <label for="sms_provider" class="form-label">SMS Provider</label>
                        <select class="form-select" id="sms_provider">
                            <option value="">Select Provider</option>
                            <option value="twilio">Twilio</option>
                            <option value="nexmo">Nexmo</option>
                            <option value="local">Local Provider</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="sms_api_key" class="form-label">API Key</label>
                        <input type="text" class="form-control" id="sms_api_key" placeholder="Your SMS API Key">
                    </div>
                    <div class="mb-3">
                        <label for="sms_sender_id" class="form-label">Sender ID</label>
                        <input type="text" class="form-control" id="sms_sender_id" placeholder="PAKVOTE">
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="sms_enabled">
                        <label class="form-check-label" for="sms_enabled">
                            Enable SMS Notifications
                        </label>
                    </div>
                    <button type="submit" class="btn btn-success">Save SMS Settings</button>
                </form>
            </div>
        </div>
    </div>

    <!-- Security Settings -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="bi bi-shield"></i> Security Settings
                </h5>
            </div>
            <div class="card-body">
                <form>
                    <div class="mb-3">
                        <label for="session_lifetime" class="form-label">Session Lifetime (minutes)</label>
                        <input type="number" class="form-control" id="session_lifetime" value="120">
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="force_https" checked>
                        <label class="form-check-label" for="force_https">
                            Force HTTPS
                        </label>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="two_factor_auth">
                        <label class="form-check-label" for="two_factor_auth">
                            Enable Two-Factor Authentication
                        </label>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="login_attempts" checked>
                        <label class="form-check-label" for="login_attempts">
                            Limit Login Attempts
                        </label>
                    </div>
                    <button type="submit" class="btn btn-warning">Save Security Settings</button>
                </form>
            </div>
        </div>
    </div>

    <!-- File Upload Settings -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header bg-secondary text-white">
                <h5 class="mb-0">
                    <i class="bi bi-cloud-upload"></i> File Upload Settings
                </h5>
            </div>
            <div class="card-body">
                <form>
                    <div class="mb-3">
                        <label for="max_file_size" class="form-label">Max File Size (MB)</label>
                        <input type="number" class="form-control" id="max_file_size" value="10">
                    </div>
                    <div class="mb-3">
                        <label for="allowed_file_types" class="form-label">Allowed File Types</label>
                        <input type="text" class="form-control" id="allowed_file_types" value="jpg,jpeg,png,pdf,doc,docx">
                        <div class="form-text">Comma-separated file extensions</div>
                    </div>
                    <div class="mb-3">
                        <label for="storage_driver" class="form-label">Storage Driver</label>
                        <select class="form-select" id="storage_driver">
                            <option value="local" selected>Local</option>
                            <option value="s3">Amazon S3</option>
                            <option value="gcs">Google Cloud Storage</option>
                        </select>
                    </div>
                    <button type="submit" class="btn btn-secondary">Save Upload Settings</button>
                </form>
            </div>
        </div>
    </div>

    <!-- ECP Integration -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0">
                    <i class="bi bi-link"></i> ECP Integration
                </h5>
            </div>
            <div class="card-body">
                <form>
                    <div class="mb-3">
                        <label for="ecp_api_url" class="form-label">ECP API URL</label>
                        <input type="url" class="form-control" id="ecp_api_url" placeholder="https://api.ecp.gov.pk">
                    </div>
                    <div class="mb-3">
                        <label for="ecp_api_key" class="form-label">ECP API Key</label>
                        <input type="text" class="form-control" id="ecp_api_key" placeholder="Your ECP API Key">
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="ecp_sync_enabled">
                        <label class="form-check-label" for="ecp_sync_enabled">
                            Enable ECP Data Synchronization
                        </label>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="ecp_validation">
                        <label class="form-check-label" for="ecp_validation">
                            Enable ECP Validation
                        </label>
                    </div>
                    <button type="submit" class="btn btn-danger">Save ECP Settings</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- System Information -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-dark text-white">
                <h5 class="mb-0">
                    <i class="bi bi-info-circle"></i> System Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <h6>Application</h6>
                        <p class="mb-1"><strong>Version:</strong> 1.0.0</p>
                        <p class="mb-1"><strong>Environment:</strong> Local</p>
                        <p class="mb-1"><strong>Debug Mode:</strong> Enabled</p>
                    </div>
                    <div class="col-md-3">
                        <h6>Server</h6>
                        <p class="mb-1"><strong>PHP Version:</strong> <?php echo e(PHP_VERSION); ?></p>
                        <p class="mb-1"><strong>Laravel Version:</strong> <?php echo e(app()->version()); ?></p>
                        <p class="mb-1"><strong>Server:</strong> <?php echo e($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'); ?></p>
                    </div>
                    <div class="col-md-3">
                        <h6>Database</h6>
                        <p class="mb-1"><strong>Driver:</strong> MySQL</p>
                        <p class="mb-1"><strong>Version:</strong> 8.0+</p>
                        <p class="mb-1"><strong>Connection:</strong> Active</p>
                    </div>
                    <div class="col-md-3">
                        <h6>Cache</h6>
                        <p class="mb-1"><strong>Driver:</strong> File</p>
                        <p class="mb-1"><strong>Session:</strong> File</p>
                        <p class="mb-1"><strong>Queue:</strong> Sync</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .card {
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\ECP\pakvote\resources\views/admin/settings.blade.php ENDPATH**/ ?>