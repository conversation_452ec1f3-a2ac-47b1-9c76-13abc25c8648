<?php

use App\Http\Controllers\Auth\AuthController;
use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\Candidate\CandidateController;
use Illuminate\Support\Facades\Route;

// Public routes
Route::get('/', function () {
    return view('welcome');
})->name('home');

// Authentication routes
Route::middleware('guest')->group(function () {
    Route::get('/login', [AuthController::class, 'showLogin'])->name('login');
    Route::post('/login', [AuthController::class, 'login']);
    Route::get('/register', [AuthController::class, 'showRegister'])->name('register');
    Route::post('/register', [AuthController::class, 'register']);
});

Route::post('/logout', [AuthController::class, 'logout'])->name('logout')->middleware('auth');

// Protected routes with tenant middleware
Route::middleware(['auth', 'tenant'])->group(function () {

    // General dashboard
    Route::get('/dashboard', function () {
        $user = auth()->user();
        return redirect()->route($user->user_type . '.dashboard');
    })->name('dashboard');

    // Admin routes
    Route::middleware(['role:admin,super_admin'])->prefix('admin')->name('admin.')->group(function () {
        Route::get('/dashboard', [AdminController::class, 'dashboard'])->name('dashboard');
        Route::get('/users', [AdminController::class, 'users'])->name('users');
        Route::get('/tenants', [AdminController::class, 'tenants'])->name('tenants');
        Route::get('/parties', [AdminController::class, 'parties'])->name('parties');
        Route::get('/constituencies', [AdminController::class, 'constituencies'])->name('constituencies');
        Route::get('/settings', [AdminController::class, 'settings'])->name('settings');
    });

    // Candidate routes
    Route::middleware(['role:candidate'])->prefix('candidate')->name('candidate.')->group(function () {
        Route::get('/dashboard', [CandidateController::class, 'dashboard'])->name('dashboard');
        Route::get('/profile', [CandidateController::class, 'profile'])->name('profile');
        Route::get('/campaign', [CandidateController::class, 'campaign'])->name('campaign');
        Route::get('/voters', [CandidateController::class, 'voters'])->name('voters');
        Route::get('/volunteers', [CandidateController::class, 'volunteers'])->name('volunteers');
        Route::get('/reports', [CandidateController::class, 'reports'])->name('reports');
    });

    // Agent routes
    Route::middleware(['role:agent'])->prefix('agent')->name('agent.')->group(function () {
        Route::get('/dashboard', function () {
            return view('agent.dashboard');
        })->name('dashboard');
    });

    // Volunteer routes
    Route::middleware(['role:volunteer'])->prefix('volunteer')->name('volunteer.')->group(function () {
        Route::get('/dashboard', function () {
            return view('volunteer.dashboard');
        })->name('dashboard');
    });
});

// Subscription expired route
Route::get('/subscription-expired', function () {
    return view('subscription.expired');
})->name('subscription.expired');
