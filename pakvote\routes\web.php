<?php

use App\Http\Controllers\Auth\AuthController;
use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\Candidate\CandidateController;
use Illuminate\Support\Facades\Route;

// Public routes
Route::get('/', function () {
    return view('welcome');
})->name('home');

// Debug route to view data (remove in production)
Route::get('/debug-data', function () {
    $users = \App\Models\User::with('tenant')->get();
    $tenants = \App\Models\Tenant::all();
    $parties = \App\Models\PoliticalParty::all();
    $constituencies = \App\Models\Constituency::all();

    return view('debug.data', compact('users', 'tenants', 'parties', 'constituencies'));
})->name('debug.data');

// Debug user roles
Route::get('/debug-user', function () {
    $user = \App\Models\User::where('email', '<EMAIL>')->first();
    if (!$user) {
        return 'User not found!';
    }

    $roles = $user->roles->pluck('name')->toArray();
    $hasSuper = $user->hasRole('super_admin');

    return [
        'user_name' => $user->name,
        'user_type' => $user->user_type,
        'tenant_id' => $user->tenant_id,
        'roles' => $roles,
        'has_super_admin' => $hasSuper,
        'is_active' => $user->is_active,
    ];
})->name('debug.user');

// Debug login process step by step
Route::get('/debug-login-process', function () {
    $email = '<EMAIL>';
    $password = 'admin123';

    // Step 1: Find user
    $user = \App\Models\User::where('email', $email)->where('is_active', true)->first();
    if (!$user) {
        return ['error' => 'User not found or inactive'];
    }

    // Step 2: Check password
    if (!\Illuminate\Support\Facades\Hash::check($password, $user->password)) {
        return ['error' => 'Password incorrect'];
    }

    // Step 3: Get tenant
    $tenant = null;
    if ($user->tenant_id) {
        $tenant = \App\Models\Tenant::find($user->tenant_id);
    }

    // Step 4: Check tenant status
    $tenantStatus = 'No tenant';
    if ($tenant) {
        $tenantStatus = [
            'exists' => true,
            'is_active' => $tenant->is_active,
            'subscription_status' => $tenant->subscription_status,
            'subscription_plan' => $tenant->subscription_plan,
            'has_active_subscription' => $tenant->hasActiveSubscription(),
            'is_free_plan' => $tenant->isFreePlan(),
        ];
    }

    // Step 5: Check roles
    $roles = $user->roles->pluck('name')->toArray();
    $hasSuper = $user->hasRole('super_admin');

    return [
        'step1_user_found' => true,
        'step2_password_correct' => true,
        'step3_tenant' => $tenantStatus,
        'step4_user_roles' => $roles,
        'step5_has_super_admin' => $hasSuper,
        'user_type' => $user->user_type,
        'should_bypass_tenant_check' => $hasSuper,
    ];
})->name('debug.login.process');

// Test login with detailed logging
Route::post('/test-login', function (\Illuminate\Http\Request $request) {
    $email = $request->input('email', '<EMAIL>');
    $password = $request->input('password', 'admin123');

    $log = [];

    try {
        // Step 1: Validate input
        $log[] = "Step 1: Validating input - Email: $email";

        // Step 2: Find user
        $user = \App\Models\User::where('email', $email)->where('is_active', true)->first();
        if (!$user) {
            $log[] = "Step 2: FAILED - User not found or inactive";
            return ['status' => 'failed', 'log' => $log];
        }
        $log[] = "Step 2: SUCCESS - User found: " . $user->name;

        // Step 3: Check password
        if (!\Illuminate\Support\Facades\Hash::check($password, $user->password)) {
            $log[] = "Step 3: FAILED - Password incorrect";
            return ['status' => 'failed', 'log' => $log];
        }
        $log[] = "Step 3: SUCCESS - Password correct";

        // Step 4: Get tenant
        $tenant = null;
        if ($user->tenant_id) {
            $tenant = \App\Models\Tenant::find($user->tenant_id);
            if ($tenant) {
                $log[] = "Step 4: SUCCESS - Tenant found: " . ($tenant->name['en'] ?? $tenant->name);
            } else {
                $log[] = "Step 4: WARNING - Tenant ID exists but tenant not found";
            }
        } else {
            $log[] = "Step 4: INFO - No tenant ID for user";
        }

        // Step 5: Login user
        \Illuminate\Support\Facades\Auth::login($user);
        $log[] = "Step 5: SUCCESS - User logged in";

        // Step 6: Set tenant session
        if ($tenant) {
            session(['current_tenant' => $tenant]);
            $log[] = "Step 6: SUCCESS - Tenant set in session";
        }

        // Step 7: Check auth status
        $isLoggedIn = \Illuminate\Support\Facades\Auth::check();
        $log[] = "Step 7: Auth check result: " . ($isLoggedIn ? 'LOGGED IN' : 'NOT LOGGED IN');

        if ($isLoggedIn) {
            $authUser = \Illuminate\Support\Facades\Auth::user();
            $log[] = "Step 7: Authenticated as: " . $authUser->name . " (" . $authUser->user_type . ")";
        }

        return [
            'status' => 'success',
            'log' => $log,
            'redirect_url' => '/working-admin',
            'user' => $user->only(['name', 'email', 'user_type']),
            'tenant' => $tenant ? $tenant->only(['name', 'domain']) : null,
        ];

    } catch (\Exception $e) {
        $log[] = "ERROR: " . $e->getMessage();
        return ['status' => 'error', 'log' => $log, 'exception' => $e->getMessage()];
    }
})->name('test.login');

// Test login form
Route::get('/test-login', function () {
    return view('test-login');
})->name('test.login.form');

// Check middleware registration
Route::get('/debug-middleware', function () {
    $app = app();
    $router = $app['router'];

    return [
        'middleware_aliases' => $app['router']->getMiddleware(),
        'middleware_groups' => $app['router']->getMiddlewareGroups(),
    ];
})->name('debug.middleware');

// Test route with tenant middleware
Route::get('/test-tenant-middleware', function () {
    return 'Tenant middleware passed!';
})->middleware(['auth', 'tenant'])->name('test.tenant.middleware');

// Test CSRF
Route::get('/test-csrf', function () {
    return view('test-csrf');
})->name('test.csrf');

Route::post('/test-csrf', function (\Illuminate\Http\Request $request) {
    return ['status' => 'success', 'message' => 'CSRF token is working!', 'data' => $request->all()];
})->name('test.csrf.post');

// Test registration without CSRF (for debugging)
Route::post('/test-register', function (\Illuminate\Http\Request $request) {
    try {
        // Create tenant first with auto-generated domain
        $domain = strtolower(str_replace(' ', '-', $request->organization_name)) . '-' . time();

        $tenant = \App\Models\Tenant::create([
            'name' => ['en' => $request->organization_name],
            'domain' => $domain,
            'subscription_plan' => \App\Models\Tenant::PLAN_FREE,
            'subscription_status' => \App\Models\Tenant::STATUS_ACTIVE,
            'contact_email' => $request->email,
            'contact_phone' => $request->phone,
        ]);

        // Create user
        $user = \App\Models\User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => \Illuminate\Support\Facades\Hash::make($request->password),
            'phone' => $request->phone,
            'cnic' => $request->cnic,
            'tenant_id' => $tenant->id,
            'user_type' => $request->user_type,
        ]);

        // Assign default role
        $user->assignRole($request->user_type);

        return [
            'status' => 'success',
            'message' => 'Registration successful!',
            'user' => $user->only(['name', 'email', 'user_type']),
            'tenant' => $tenant->only(['name', 'domain'])
        ];

    } catch (\Exception $e) {
        return [
            'status' => 'error',
            'message' => 'Registration failed: ' . $e->getMessage()
        ];
    }
})->withoutMiddleware([\App\Http\Middleware\VerifyCsrfToken::class])->name('test.register');

// Test registration form
Route::get('/test-register', function () {
    return view('test-register');
})->name('test.register.form');

// Debug dashboard routes (remove in production)
Route::get('/debug-admin', function () {
    $stats = [
        'total_tenants' => \App\Models\Tenant::count(),
        'active_tenants' => \App\Models\Tenant::where('is_active', true)->count(),
        'total_users' => \App\Models\User::count(),
        'total_candidates' => \App\Models\Candidate::count(),
        'total_parties' => \App\Models\PoliticalParty::count(),
        'total_constituencies' => \App\Models\Constituency::count(),
    ];
    $recent_tenants = \App\Models\Tenant::latest()->take(5)->get();
    $recent_users = \App\Models\User::latest()->take(5)->get();

    return view('admin.dashboard', compact('stats', 'recent_tenants', 'recent_users'));
})->name('debug.admin');

Route::get('/debug-candidate', function () {
    $stats = [
        'total_campaigns' => 0,
        'total_voters' => \App\Models\Voter::count(),
        'active_volunteers' => 0,
        'pending_tasks' => 0,
    ];
    $candidate = null;

    return view('candidate.dashboard', compact('stats', 'candidate'));
})->name('debug.candidate');

// Debug login test (remove in production)
Route::get('/debug-login', function () {
    $user = \App\Models\User::where('email', '<EMAIL>')->first();
    if ($user) {
        \Illuminate\Support\Facades\Auth::login($user);
        $tenant = \App\Models\Tenant::find($user->tenant_id);
        if ($tenant) {
            session(['current_tenant' => $tenant]);
        }
        return redirect('/debug-admin')->with('success', 'Debug login successful!');
    }
    return redirect()->route('login')->with('error', 'Debug user not found!');
})->name('debug.login');

// Simple dashboard routes without middleware (for testing)
Route::get('/simple-dashboard', function () {
    if (!\Illuminate\Support\Facades\Auth::check()) {
        return redirect()->route('login');
    }
    $user = \Illuminate\Support\Facades\Auth::user();
    return "Logged in as: " . $user->name . " (" . $user->email . ") - User Type: " . $user->user_type;
})->name('simple.dashboard');

// Working admin dashboard without problematic middleware
Route::get('/working-admin', function () {
    if (!\Illuminate\Support\Facades\Auth::check()) {
        return redirect()->route('login');
    }

    $stats = [
        'total_tenants' => \App\Models\Tenant::count(),
        'active_tenants' => \App\Models\Tenant::where('is_active', true)->count(),
        'total_users' => \App\Models\User::count(),
        'total_candidates' => \App\Models\Candidate::count(),
        'total_parties' => \App\Models\PoliticalParty::count(),
        'total_constituencies' => \App\Models\Constituency::count(),
    ];
    $recent_tenants = \App\Models\Tenant::latest()->take(5)->get();
    $recent_users = \App\Models\User::latest()->take(5)->get();

    return view('admin.dashboard', compact('stats', 'recent_tenants', 'recent_users'));
})->name('working.admin');

// Authentication routes
Route::middleware('guest')->group(function () {
    Route::get('/login', [AuthController::class, 'showLogin'])->name('login');
    Route::post('/login', [AuthController::class, 'login']);
    Route::get('/register', [AuthController::class, 'showRegister'])->name('register');
    Route::post('/register', [AuthController::class, 'register']);
});

Route::post('/logout', [AuthController::class, 'logout'])->name('logout')->middleware('auth');

// Protected routes with tenant middleware
Route::middleware(['auth', 'tenant'])->group(function () {

    // General dashboard
    Route::get('/dashboard', function () {
        $user = auth()->user();
        return redirect()->route($user->user_type . '.dashboard');
    })->name('dashboard');

    // Admin routes
    Route::middleware(['role:admin,super_admin'])->prefix('admin')->name('admin.')->group(function () {
        Route::get('/dashboard', [AdminController::class, 'dashboard'])->name('dashboard');

        // Users management
        Route::get('/users', [AdminController::class, 'users'])->name('users');
        Route::post('/users', [AdminController::class, 'storeUser'])->name('users.store');
        Route::get('/users/{user}/edit', [AdminController::class, 'editUser'])->name('users.edit');
        Route::put('/users/{user}', [AdminController::class, 'updateUser'])->name('users.update');
        Route::delete('/users/{user}', [AdminController::class, 'deleteUser'])->name('users.delete');

        // Tenants management
        Route::get('/tenants', [AdminController::class, 'tenants'])->name('tenants');
        Route::post('/tenants', [AdminController::class, 'storeTenant'])->name('tenants.store');
        Route::get('/tenants/{tenant}/edit', [AdminController::class, 'editTenant'])->name('tenants.edit');
        Route::put('/tenants/{tenant}', [AdminController::class, 'updateTenant'])->name('tenants.update');
        Route::delete('/tenants/{tenant}', [AdminController::class, 'deleteTenant'])->name('tenants.delete');

        // Political parties management
        Route::get('/parties', [AdminController::class, 'parties'])->name('parties');
        Route::post('/parties', [AdminController::class, 'storeParty'])->name('parties.store');
        Route::get('/parties/{party}/edit', [AdminController::class, 'editParty'])->name('parties.edit');
        Route::put('/parties/{party}', [AdminController::class, 'updateParty'])->name('parties.update');
        Route::delete('/parties/{party}', [AdminController::class, 'deleteParty'])->name('parties.delete');

        // Constituencies management
        Route::get('/constituencies', [AdminController::class, 'constituencies'])->name('constituencies');
        Route::post('/constituencies', [AdminController::class, 'storeConstituency'])->name('constituencies.store');
        Route::get('/constituencies/{constituency}/edit', [AdminController::class, 'editConstituency'])->name('constituencies.edit');
        Route::put('/constituencies/{constituency}', [AdminController::class, 'updateConstituency'])->name('constituencies.update');
        Route::delete('/constituencies/{constituency}', [AdminController::class, 'deleteConstituency'])->name('constituencies.delete');

        Route::get('/settings', [AdminController::class, 'settings'])->name('settings');
    });

    // Candidate routes
    Route::middleware(['role:candidate'])->prefix('candidate')->name('candidate.')->group(function () {
        Route::get('/dashboard', [CandidateController::class, 'dashboard'])->name('dashboard');
        Route::get('/profile', [CandidateController::class, 'profile'])->name('profile');
        Route::get('/campaign', [CandidateController::class, 'campaign'])->name('campaign');
        Route::get('/voters', [CandidateController::class, 'voters'])->name('voters');
        Route::get('/volunteers', [CandidateController::class, 'volunteers'])->name('volunteers');
        Route::get('/reports', [CandidateController::class, 'reports'])->name('reports');
    });

    // Agent routes
    Route::middleware(['role:agent'])->prefix('agent')->name('agent.')->group(function () {
        Route::get('/dashboard', function () {
            return view('agent.dashboard');
        })->name('dashboard');
    });

    // Volunteer routes
    Route::middleware(['role:volunteer'])->prefix('volunteer')->name('volunteer.')->group(function () {
        Route::get('/dashboard', function () {
            return view('volunteer.dashboard');
        })->name('dashboard');
    });
});

// Subscription expired route
Route::get('/subscription-expired', function () {
    return view('subscription.expired');
})->name('subscription.expired');
