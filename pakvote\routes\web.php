<?php

use App\Http\Controllers\Auth\AuthController;
use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\Candidate\CandidateController;
use Illuminate\Support\Facades\Route;

// Public routes
Route::get('/', function () {
    return view('welcome');
})->name('home');

// Debug route to view data (remove in production)
Route::get('/debug-data', function () {
    $users = \App\Models\User::with('tenant')->get();
    $tenants = \App\Models\Tenant::all();
    $parties = \App\Models\PoliticalParty::all();
    $constituencies = \App\Models\Constituency::all();

    return view('debug.data', compact('users', 'tenants', 'parties', 'constituencies'));
})->name('debug.data');

// Debug dashboard routes (remove in production)
Route::get('/debug-admin', function () {
    $stats = [
        'total_tenants' => \App\Models\Tenant::count(),
        'active_tenants' => \App\Models\Tenant::where('is_active', true)->count(),
        'total_users' => \App\Models\User::count(),
        'total_candidates' => \App\Models\Candidate::count(),
        'total_parties' => \App\Models\PoliticalParty::count(),
        'total_constituencies' => \App\Models\Constituency::count(),
    ];
    $recent_tenants = \App\Models\Tenant::latest()->take(5)->get();
    $recent_users = \App\Models\User::latest()->take(5)->get();

    return view('admin.dashboard', compact('stats', 'recent_tenants', 'recent_users'));
})->name('debug.admin');

Route::get('/debug-candidate', function () {
    $stats = [
        'total_campaigns' => 0,
        'total_voters' => \App\Models\Voter::count(),
        'active_volunteers' => 0,
        'pending_tasks' => 0,
    ];
    $candidate = null;

    return view('candidate.dashboard', compact('stats', 'candidate'));
})->name('debug.candidate');

// Debug login test (remove in production)
Route::get('/debug-login', function () {
    $user = \App\Models\User::where('email', '<EMAIL>')->first();
    if ($user) {
        \Illuminate\Support\Facades\Auth::login($user);
        $tenant = \App\Models\Tenant::find($user->tenant_id);
        if ($tenant) {
            session(['current_tenant' => $tenant]);
        }
        return redirect('/debug-admin')->with('success', 'Debug login successful!');
    }
    return redirect()->route('login')->with('error', 'Debug user not found!');
})->name('debug.login');

// Simple dashboard routes without middleware (for testing)
Route::get('/simple-dashboard', function () {
    if (!\Illuminate\Support\Facades\Auth::check()) {
        return redirect()->route('login');
    }
    $user = \Illuminate\Support\Facades\Auth::user();
    return "Logged in as: " . $user->name . " (" . $user->email . ") - User Type: " . $user->user_type;
})->name('simple.dashboard');

// Working admin dashboard without problematic middleware
Route::get('/working-admin', function () {
    if (!\Illuminate\Support\Facades\Auth::check()) {
        return redirect()->route('login');
    }

    $stats = [
        'total_tenants' => \App\Models\Tenant::count(),
        'active_tenants' => \App\Models\Tenant::where('is_active', true)->count(),
        'total_users' => \App\Models\User::count(),
        'total_candidates' => \App\Models\Candidate::count(),
        'total_parties' => \App\Models\PoliticalParty::count(),
        'total_constituencies' => \App\Models\Constituency::count(),
    ];
    $recent_tenants = \App\Models\Tenant::latest()->take(5)->get();
    $recent_users = \App\Models\User::latest()->take(5)->get();

    return view('admin.dashboard', compact('stats', 'recent_tenants', 'recent_users'));
})->name('working.admin');

// Authentication routes
Route::middleware('guest')->group(function () {
    Route::get('/login', [AuthController::class, 'showLogin'])->name('login');
    Route::post('/login', [AuthController::class, 'login']);
    Route::get('/register', [AuthController::class, 'showRegister'])->name('register');
    Route::post('/register', [AuthController::class, 'register']);
});

Route::post('/logout', [AuthController::class, 'logout'])->name('logout')->middleware('auth');

// Protected routes with tenant middleware
Route::middleware(['auth', 'tenant'])->group(function () {

    // General dashboard
    Route::get('/dashboard', function () {
        $user = auth()->user();
        return redirect()->route($user->user_type . '.dashboard');
    })->name('dashboard');

    // Admin routes
    Route::middleware(['role:admin,super_admin'])->prefix('admin')->name('admin.')->group(function () {
        Route::get('/dashboard', [AdminController::class, 'dashboard'])->name('dashboard');
        Route::get('/users', [AdminController::class, 'users'])->name('users');
        Route::get('/tenants', [AdminController::class, 'tenants'])->name('tenants');
        Route::get('/parties', [AdminController::class, 'parties'])->name('parties');
        Route::get('/constituencies', [AdminController::class, 'constituencies'])->name('constituencies');
        Route::get('/settings', [AdminController::class, 'settings'])->name('settings');
    });

    // Candidate routes
    Route::middleware(['role:candidate'])->prefix('candidate')->name('candidate.')->group(function () {
        Route::get('/dashboard', [CandidateController::class, 'dashboard'])->name('dashboard');
        Route::get('/profile', [CandidateController::class, 'profile'])->name('profile');
        Route::get('/campaign', [CandidateController::class, 'campaign'])->name('campaign');
        Route::get('/voters', [CandidateController::class, 'voters'])->name('voters');
        Route::get('/volunteers', [CandidateController::class, 'volunteers'])->name('volunteers');
        Route::get('/reports', [CandidateController::class, 'reports'])->name('reports');
    });

    // Agent routes
    Route::middleware(['role:agent'])->prefix('agent')->name('agent.')->group(function () {
        Route::get('/dashboard', function () {
            return view('agent.dashboard');
        })->name('dashboard');
    });

    // Volunteer routes
    Route::middleware(['role:volunteer'])->prefix('volunteer')->name('volunteer.')->group(function () {
        Route::get('/dashboard', function () {
            return view('volunteer.dashboard');
        })->name('dashboard');
    });
});

// Subscription expired route
Route::get('/subscription-expired', function () {
    return view('subscription.expired');
})->name('subscription.expired');
