<?php $__env->startSection('title', 'Edit Organization'); ?>

<?php $__env->startSection('sidebar'); ?>
<div class="list-group list-group-flush">
    <a href="<?php echo e(route('admin.dashboard')); ?>" class="list-group-item list-group-item-action">
        <i class="bi bi-speedometer2"></i> Dashboard
    </a>
    <a href="<?php echo e(route('admin.users')); ?>" class="list-group-item list-group-item-action">
        <i class="bi bi-people"></i> Users
    </a>
    <a href="<?php echo e(route('admin.tenants')); ?>" class="list-group-item list-group-item-action active">
        <i class="bi bi-building"></i> Organizations
    </a>
    <a href="<?php echo e(route('admin.parties')); ?>" class="list-group-item list-group-item-action">
        <i class="bi bi-flag"></i> Political Parties
    </a>
    <a href="<?php echo e(route('admin.constituencies')); ?>" class="list-group-item list-group-item-action">
        <i class="bi bi-geo-alt"></i> Constituencies
    </a>
    <a href="<?php echo e(route('admin.settings')); ?>" class="list-group-item list-group-item-action">
        <i class="bi bi-gear"></i> Settings
    </a>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="bi bi-pencil text-primary"></i> Edit Organization
    </h1>
    <div>
        <a href="<?php echo e(route('admin.tenants')); ?>" class="btn btn-secondary">
            <i class="bi bi-arrow-left"></i> Back to Organizations
        </a>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="bi bi-building"></i> Organization Details
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="<?php echo e(route('admin.tenants.update', $tenant)); ?>">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('PUT'); ?>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">Organization Name *</label>
                                <input type="text" class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                       id="name" name="name" value="<?php echo e(old('name', $tenant->name['en'] ?? $tenant->name)); ?>" required>
                                <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            
                            <div class="mb-3">
                                <label for="domain" class="form-label">Domain *</label>
                                <input type="text" class="form-control <?php $__errorArgs = ['domain'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                       id="domain" name="domain" value="<?php echo e(old('domain', $tenant->domain)); ?>" required>
                                <?php $__errorArgs = ['domain'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            
                            <div class="mb-3">
                                <label for="contact_email" class="form-label">Contact Email *</label>
                                <input type="email" class="form-control <?php $__errorArgs = ['contact_email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                       id="contact_email" name="contact_email" value="<?php echo e(old('contact_email', $tenant->contact_email)); ?>" required>
                                <?php $__errorArgs = ['contact_email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            
                            <div class="mb-3">
                                <label for="contact_phone" class="form-label">Contact Phone</label>
                                <input type="tel" class="form-control <?php $__errorArgs = ['contact_phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                       id="contact_phone" name="contact_phone" value="<?php echo e(old('contact_phone', $tenant->contact_phone)); ?>">
                                <?php $__errorArgs = ['contact_phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="subscription_plan" class="form-label">Subscription Plan *</label>
                                <select class="form-select <?php $__errorArgs = ['subscription_plan'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                        id="subscription_plan" name="subscription_plan" required>
                                    <option value="free" <?php echo e(old('subscription_plan', $tenant->subscription_plan) === 'free' ? 'selected' : ''); ?>>Free</option>
                                    <option value="pro" <?php echo e(old('subscription_plan', $tenant->subscription_plan) === 'pro' ? 'selected' : ''); ?>>Pro</option>
                                    <option value="enterprise" <?php echo e(old('subscription_plan', $tenant->subscription_plan) === 'enterprise' ? 'selected' : ''); ?>>Enterprise</option>
                                </select>
                                <?php $__errorArgs = ['subscription_plan'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            
                            <div class="mb-3">
                                <label for="subscription_status" class="form-label">Subscription Status *</label>
                                <select class="form-select <?php $__errorArgs = ['subscription_status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                        id="subscription_status" name="subscription_status" required>
                                    <option value="active" <?php echo e(old('subscription_status', $tenant->subscription_status) === 'active' ? 'selected' : ''); ?>>Active</option>
                                    <option value="inactive" <?php echo e(old('subscription_status', $tenant->subscription_status) === 'inactive' ? 'selected' : ''); ?>>Inactive</option>
                                    <option value="suspended" <?php echo e(old('subscription_status', $tenant->subscription_status) === 'suspended' ? 'selected' : ''); ?>>Suspended</option>
                                    <option value="cancelled" <?php echo e(old('subscription_status', $tenant->subscription_status) === 'cancelled' ? 'selected' : ''); ?>>Cancelled</option>
                                </select>
                                <?php $__errorArgs = ['subscription_status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            
                            <div class="mb-3">
                                <label for="address" class="form-label">Address</label>
                                <textarea class="form-control <?php $__errorArgs = ['address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                          id="address" name="address" rows="3"><?php echo e(old('address', $tenant->address)); ?></textarea>
                                <?php $__errorArgs = ['address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                       <?php echo e(old('is_active', $tenant->is_active) ? 'checked' : ''); ?>>
                                <label class="form-check-label" for="is_active">
                                    Organization is Active
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="d-flex justify-content-between">
                        <div>
                            <small class="text-muted">
                                <i class="bi bi-info-circle"></i> 
                                Created: <?php echo e($tenant->created_at->format('M j, Y g:i A')); ?>

                                <?php if($tenant->updated_at != $tenant->created_at): ?>
                                    | Updated: <?php echo e($tenant->updated_at->format('M j, Y g:i A')); ?>

                                <?php endif; ?>
                            </small>
                        </div>
                        <div>
                            <button type="button" class="btn btn-secondary" onclick="history.back()">Cancel</button>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check"></i> Update Organization
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Organization Statistics -->
        <div class="card mt-4">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="bi bi-graph-up"></i> Organization Statistics
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 text-center">
                        <h4 class="text-primary"><?php echo e($tenant->users()->count()); ?></h4>
                        <small class="text-muted">Total Users</small>
                    </div>
                    <div class="col-md-3 text-center">
                        <h4 class="text-success"><?php echo e($tenant->users()->where('is_active', true)->count()); ?></h4>
                        <small class="text-muted">Active Users</small>
                    </div>
                    <div class="col-md-3 text-center">
                        <h4 class="text-warning"><?php echo e($tenant->users()->where('user_type', 'candidate')->count()); ?></h4>
                        <small class="text-muted">Candidates</small>
                    </div>
                    <div class="col-md-3 text-center">
                        <h4 class="text-info"><?php echo e($tenant->users()->where('user_type', 'volunteer')->count()); ?></h4>
                        <small class="text-muted">Volunteers</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .card {
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\ECP\pakvote\resources\views/admin/edit-tenant.blade.php ENDPATH**/ ?>