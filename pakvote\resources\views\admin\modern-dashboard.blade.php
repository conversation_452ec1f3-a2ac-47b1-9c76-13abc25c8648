@extends('layouts.modern')

@section('title', 'Admin Dashboard')

@section('sidebar')
@endsection

@section('content')
<div class="dashboard-container">
    <!-- Welcome Section -->
    <div class="welcome-section mb-4">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="dashboard-title">
                    <i class="bi bi-speedometer2 text-primary"></i>
                    Welcome back, {{ auth()->user()->name }}!
                </h1>
                <p class="dashboard-subtitle">
                    Here's what's happening with your election management system today.
                </p>
            </div>
            <div class="col-md-4 text-end">
                <div class="dashboard-date">
                    <i class="bi bi-calendar3"></i>
                    {{ now()->format('l, F j, Y') }}
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-4">
            <x-card 
                title="Total Users" 
                icon="bi bi-people" 
                headerClass="bg-primary text-white"
                hover="true"
                :stats="[
                    [
                        'value' => \App\Models\User::count(),
                        'label' => 'Registered Users',
                        'icon' => 'bi bi-person-plus',
                        'change' => 12
                    ]
                ]">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <small class="text-muted">Active: {{ \App\Models\User::where('is_active', true)->count() }}</small><br>
                        <small class="text-muted">Inactive: {{ \App\Models\User::where('is_active', false)->count() }}</small>
                    </div>
                    <a href="{{ route('admin.users') }}" class="btn btn-sm btn-outline-primary">
                        <i class="bi bi-arrow-right"></i>
                    </a>
                </div>
            </x-card>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <x-card 
                title="Organizations" 
                icon="bi bi-building" 
                headerClass="bg-success text-white"
                hover="true"
                :stats="[
                    [
                        'value' => \App\Models\Tenant::count(),
                        'label' => 'Total Organizations',
                        'icon' => 'bi bi-building-add',
                        'change' => 8
                    ]
                ]">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <small class="text-muted">Active: {{ \App\Models\Tenant::where('is_active', true)->count() }}</small><br>
                        <small class="text-muted">Pro Plans: {{ \App\Models\Tenant::where('subscription_plan', 'pro')->count() }}</small>
                    </div>
                    <a href="{{ route('admin.tenants') }}" class="btn btn-sm btn-outline-success">
                        <i class="bi bi-arrow-right"></i>
                    </a>
                </div>
            </x-card>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <x-card 
                title="Political Parties" 
                icon="bi bi-flag" 
                headerClass="bg-warning text-dark"
                hover="true"
                :stats="[
                    [
                        'value' => \App\Models\PoliticalParty::count(),
                        'label' => 'Registered Parties',
                        'icon' => 'bi bi-flag-fill',
                        'change' => 5
                    ]
                ]">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <small class="text-muted">Active: {{ \App\Models\PoliticalParty::where('is_active', true)->count() }}</small><br>
                        <small class="text-muted">ECP Registered: {{ \App\Models\PoliticalParty::whereNotNull('ecp_registration_number')->count() }}</small>
                    </div>
                    <a href="{{ route('admin.parties') }}" class="btn btn-sm btn-outline-warning">
                        <i class="bi bi-arrow-right"></i>
                    </a>
                </div>
            </x-card>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <x-card 
                title="Constituencies" 
                icon="bi bi-geo-alt" 
                headerClass="bg-info text-white"
                hover="true"
                :stats="[
                    [
                        'value' => \App\Models\Constituency::count(),
                        'label' => 'Total Constituencies',
                        'icon' => 'bi bi-geo-alt-fill',
                        'change' => 3
                    ]
                ]">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <small class="text-muted">National: {{ \App\Models\Constituency::where('type', 'national')->count() }}</small><br>
                        <small class="text-muted">Provincial: {{ \App\Models\Constituency::where('type', 'provincial')->count() }}</small>
                    </div>
                    <a href="{{ route('admin.constituencies') }}" class="btn btn-sm btn-outline-info">
                        <i class="bi bi-arrow-right"></i>
                    </a>
                </div>
            </x-card>
        </div>
    </div>

    <!-- Charts and Analytics -->
    <div class="row mb-4">
        <div class="col-lg-8 mb-4">
            <x-card 
                title="User Registration Trends" 
                subtitle="Last 30 days"
                icon="bi bi-graph-up" 
                headerClass="bg-primary text-white">
                <div class="chart-container" style="height: 300px;">
                    <canvas id="userRegistrationChart"></canvas>
                </div>
            </x-card>
        </div>

        <div class="col-lg-4 mb-4">
            <x-card 
                title="System Status" 
                icon="bi bi-shield-check" 
                headerClass="bg-success text-white">
                <div class="status-list">
                    <div class="status-item">
                        <div class="status-indicator online"></div>
                        <div class="status-details">
                            <strong>Database</strong>
                            <small class="text-muted d-block">Connected</small>
                        </div>
                    </div>
                    <div class="status-item">
                        <div class="status-indicator online"></div>
                        <div class="status-details">
                            <strong>Email Service</strong>
                            <small class="text-muted d-block">Operational</small>
                        </div>
                    </div>
                    <div class="status-item">
                        <div class="status-indicator online"></div>
                        <div class="status-details">
                            <strong>SMS Gateway</strong>
                            <small class="text-muted d-block">Active</small>
                        </div>
                    </div>
                    <div class="status-item">
                        <div class="status-indicator warning"></div>
                        <div class="status-details">
                            <strong>ECP Integration</strong>
                            <small class="text-muted d-block">Testing</small>
                        </div>
                    </div>
                </div>
            </x-card>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="row">
        <div class="col-lg-6 mb-4">
            <x-card 
                title="Recent Users" 
                icon="bi bi-person-plus" 
                headerClass="bg-secondary text-white">
                <x-slot name="actions">
                    <a href="{{ route('admin.users') }}" class="btn btn-sm btn-outline-light">View All</a>
                </x-slot>
                
                <div class="recent-users">
                    @foreach(\App\Models\User::latest()->take(5)->get() as $user)
                        <div class="user-item">
                            <div class="user-avatar">
                                <img src="https://ui-avatars.com/api/?name={{ urlencode($user->name) }}&background=2d5a27&color=fff" 
                                     alt="Avatar" class="avatar-img">
                            </div>
                            <div class="user-details">
                                <strong>{{ $user->name }}</strong>
                                <small class="text-muted d-block">{{ $user->email }}</small>
                                <small class="text-muted">{{ $user->created_at->diffForHumans() }}</small>
                            </div>
                            <div class="user-badge">
                                <span class="badge bg-{{ $user->user_type === 'admin' ? 'danger' : 'primary' }}">
                                    {{ ucfirst($user->user_type) }}
                                </span>
                            </div>
                        </div>
                    @endforeach
                </div>
            </x-card>
        </div>

        <div class="col-lg-6 mb-4">
            <x-card 
                title="Quick Actions" 
                icon="bi bi-lightning" 
                headerClass="bg-dark text-white">
                <div class="quick-actions">
                    <a href="{{ route('admin.users.create') }}" class="action-item">
                        <div class="action-icon bg-primary">
                            <i class="bi bi-person-plus"></i>
                        </div>
                        <div class="action-details">
                            <strong>Add New User</strong>
                            <small class="text-muted d-block">Create a new user account</small>
                        </div>
                    </a>
                    
                    <a href="{{ route('admin.tenants.create') }}" class="action-item">
                        <div class="action-icon bg-success">
                            <i class="bi bi-building-add"></i>
                        </div>
                        <div class="action-details">
                            <strong>Add Organization</strong>
                            <small class="text-muted d-block">Register new organization</small>
                        </div>
                    </a>
                    
                    <a href="{{ route('admin.parties.create') }}" class="action-item">
                        <div class="action-icon bg-warning">
                            <i class="bi bi-flag-fill"></i>
                        </div>
                        <div class="action-details">
                            <strong>Add Political Party</strong>
                            <small class="text-muted d-block">Register new party</small>
                        </div>
                    </a>
                    
                    <a href="{{ route('admin.settings') }}" class="action-item">
                        <div class="action-icon bg-info">
                            <i class="bi bi-gear-fill"></i>
                        </div>
                        <div class="action-details">
                            <strong>System Settings</strong>
                            <small class="text-muted d-block">Configure system</small>
                        </div>
                    </a>
                </div>
            </x-card>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.dashboard-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 8px;
}

.dashboard-subtitle {
    color: var(--text-secondary);
    font-size: 1.1rem;
    margin-bottom: 0;
}

.dashboard-date {
    background: var(--secondary-color);
    padding: 10px 15px;
    border-radius: 8px;
    color: var(--text-secondary);
    font-weight: 500;
}

.status-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 12px;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    flex-shrink: 0;
}

.status-indicator.online {
    background: #28a745;
    animation: pulse 2s infinite;
}

.status-indicator.warning {
    background: #ffc107;
}

.recent-users {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.user-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 10px;
    border-radius: 8px;
    background: var(--secondary-color);
}

.user-avatar .avatar-img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

.user-details {
    flex: 1;
}

.quick-actions {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.action-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    border-radius: 8px;
    background: var(--secondary-color);
    text-decoration: none;
    color: var(--text-primary);
    transition: var(--transition);
}

.action-item:hover {
    background: var(--border-color);
    transform: translateX(5px);
    color: var(--text-primary);
}

.action-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.1rem;
}

.action-details {
    flex: 1;
}
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// User Registration Chart
const ctx = document.getElementById('userRegistrationChart').getContext('2d');
new Chart(ctx, {
    type: 'line',
    data: {
        labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
        datasets: [{
            label: 'New Users',
            data: [12, 19, 8, 15],
            borderColor: '#2d5a27',
            backgroundColor: 'rgba(45, 90, 39, 0.1)',
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});
</script>
@endpush
